"""Vector database storage for email templates."""
import os
import hashlib
import tempfile
from typing import Dict, List, Any, Optional, Tuple

from langchain_chroma import Chroma
from langchain_core.embeddings import Embeddings

class VectorStore:
    """Vector database storage for email templates."""
    
    def __init__(self, db_path: str, embeddings: Embeddings):
        """Initialize the vector store."""
        # Ensure directory exists
        os.makedirs(db_path, exist_ok=True)
        
        # Initialize ChromaDB through LangChain
        try:
            print(f"Initializing ChromaDB with persist_directory={db_path}")
            self.vectorstore = Chroma(
                persist_directory=db_path,
                embedding_function=embeddings,
                collection_name="email_templates"
            )
            # Print collection stats to verify it's loading correctly
            collection_count = self.vectorstore._collection.count()
            print(f"Loaded existing collection with {collection_count} documents")
        except Exception as e:
            print(f"Error initializing ChromaDB: {e}")
            # Try with a temporary directory if the specified path is not writable
            temp_dir = tempfile.mkdtemp()
            print(f"Falling back to temporary directory: {temp_dir}")
            self.vectorstore = Chroma(
                persist_directory=temp_dir,
                embedding_function=embeddings,
                collection_name="email_templates"
            )
    
    def check_duplicate(self, doc_id: str) -> bool:
        """Check if a document ID already exists in the collection."""
        existing_docs = self.vectorstore._collection.get(ids=[doc_id])
        return existing_docs and len(existing_docs['ids']) > 0
    
    def find_similar(self, text: str, threshold: float = 0.8) -> Tuple[bool, Optional[Dict[str, Any]], Optional[float]]:
        """Find similar documents in the vector store.
        
        Returns:
            Tuple containing:
            - Boolean indicating if a similar document was found
            - Metadata of the similar document (if found)
            - Similarity score (if found)
        """
        results = self.vectorstore.similarity_search_with_score(
            text, 
            k=1
        )
        
        if not results or results[0][1] > threshold:
            return False, None, None
        
        similar_doc = results[0][0]
        similarity_score = results[0][1]
        
        return True, similar_doc.metadata, similarity_score
    
    def add_document(self, text: str, metadata: Dict[str, Any], doc_id: str) -> None:
        """Add a document to the vector store."""
        self.vectorstore.add_texts(
            texts=[text],
            metadatas=[metadata],
            ids=[doc_id]
        )