"""Vector database storage for email templates."""
import tempfile
from typing import Dict, List, Any, Optional, Tu<PERSON>

from langchain_chroma import Chroma
from langchain_core.embeddings import Embeddings
import chromadb

class VectorStore:
    """Vector database storage for email templates."""

    def __init__(self, embeddings: Embeddings, chromadb_host: str = "localhost", chromadb_port: int = 8000):
        """Initialize the vector store with ChromaDB HTTP client.

        Args:
            embeddings: The embeddings function to use
            chromadb_host: ChromaDB server host (default: localhost)
            chromadb_port: ChromaDB server port (default: 8000)
        """
        self.chromadb_host = chromadb_host
        self.chromadb_port = chromadb_port

        # Initialize ChromaDB HTTP client
        try:
            print(f"Connecting to ChromaDB at http://{chromadb_host}:{chromadb_port}")

            # Create ChromaDB HTTP client
            chroma_client = chromadb.HttpClient(
                host=chromadb_host,
                port=chromadb_port
            )

            # Initialize ChromaDB through LangChain with HTTP client
            self.vectorstore = Chroma(
                client=chroma_client,
                embedding_function=embeddings,
                collection_name="email_templates"
            )

            # Print collection stats to verify it's loading correctly
            collection_count = self.vectorstore._collection.count()
            print(f"Connected to ChromaDB collection with {collection_count} documents")

        except Exception as e:
            print(f"Error connecting to ChromaDB at {chromadb_host}:{chromadb_port}: {e}")
            print("Falling back to local ChromaDB instance...")

            # Fallback to local file-based ChromaDB if HTTP connection fails
            temp_dir = tempfile.mkdtemp()
            print(f"Using temporary directory for local ChromaDB: {temp_dir}")
            self.vectorstore = Chroma(
                persist_directory=temp_dir,
                embedding_function=embeddings,
                collection_name="email_templates"
            )
    
    def check_duplicate(self, doc_id: str) -> bool:
        """Check if a document ID already exists in the collection."""
        existing_docs = self.vectorstore._collection.get(ids=[doc_id])
        return existing_docs and len(existing_docs['ids']) > 0
    
    def find_similar(self, text: str, threshold: float = 0.8) -> Tuple[bool, Optional[Dict[str, Any]], Optional[float]]:
        """Find similar documents in the vector store.
        
        Returns:
            Tuple containing:
            - Boolean indicating if a similar document was found
            - Metadata of the similar document (if found)
            - Similarity score (if found)
        """
        results = self.vectorstore.similarity_search_with_score(
            text, 
            k=1
        )
        
        if not results or results[0][1] > threshold:
            return False, None, None
        
        similar_doc = results[0][0]
        similarity_score = results[0][1]
        
        return True, similar_doc.metadata, similarity_score
    
    def add_document(self, text: str, metadata: Dict[str, Any], doc_id: str) -> None:
        """Add a document to the vector store."""
        self.vectorstore.add_texts(
            texts=[text],
            metadatas=[metadata],
            ids=[doc_id]
        )