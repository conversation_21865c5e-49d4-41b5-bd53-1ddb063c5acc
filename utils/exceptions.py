"""Custom exceptions for the email scanner application."""


class EmailScannerError(Exception):
    """Base exception for all email scanner errors."""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ConfigurationError(EmailScannerError):
    """Raised when there's a configuration error."""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)
        self.config_key = config_key


class DatabaseError(EmailScannerError):
    """Raised when there's a database error."""
    
    def __init__(self, message: str, operation: str = None, table: str = None, **kwargs):
        super().__init__(message, error_code="DB_ERROR", **kwargs)
        self.operation = operation
        self.table = table


class ProcessingError(EmailScannerError):
    """Raised when there's an email processing error."""
    
    def __init__(self, message: str, email_path: str = None, stage: str = None, **kwargs):
        super().__init__(message, error_code="PROCESSING_ERROR", **kwargs)
        self.email_path = email_path
        self.stage = stage


class QueueError(EmailScannerError):
    """Raised when there's a queue operation error."""
    
    def __init__(self, message: str, queue_name: str = None, job_id: str = None, **kwargs):
        super().__init__(message, error_code="QUEUE_ERROR", **kwargs)
        self.queue_name = queue_name
        self.job_id = job_id


class VectorStoreError(EmailScannerError):
    """Raised when there's a vector store error."""
    
    def __init__(self, message: str, operation: str = None, collection: str = None, **kwargs):
        super().__init__(message, error_code="VECTOR_STORE_ERROR", **kwargs)
        self.operation = operation
        self.collection = collection


class LLMError(EmailScannerError):
    """Raised when there's an LLM service error."""
    
    def __init__(self, message: str, model: str = None, operation: str = None, **kwargs):
        super().__init__(message, error_code="LLM_ERROR", **kwargs)
        self.model = model
        self.operation = operation


class BusinessManagerError(EmailScannerError):
    """Raised when there's a business manager error."""
    
    def __init__(self, message: str, business_name: str = None, operation: str = None, **kwargs):
        super().__init__(message, error_code="BUSINESS_ERROR", **kwargs)
        self.business_name = business_name
        self.operation = operation


class ValidationError(EmailScannerError):
    """Raised when there's a validation error."""
    
    def __init__(self, message: str, field: str = None, value: str = None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field
        self.value = value


class RetryableError(EmailScannerError):
    """Base class for errors that can be retried."""
    
    def __init__(self, message: str, retry_after: int = None, max_retries: int = 3, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after
        self.max_retries = max_retries


class TemporaryDatabaseError(RetryableError, DatabaseError):
    """Raised for temporary database errors that can be retried."""
    pass


class TemporaryQueueError(RetryableError, QueueError):
    """Raised for temporary queue errors that can be retried."""
    pass


class TemporaryVectorStoreError(RetryableError, VectorStoreError):
    """Raised for temporary vector store errors that can be retried."""
    pass


class TemporaryLLMError(RetryableError, LLMError):
    """Raised for temporary LLM errors that can be retried."""
    pass


def handle_exception(logger, exception: Exception, context: str = None) -> EmailScannerError:
    """Convert generic exceptions to EmailScannerError with proper logging."""
    
    if isinstance(exception, EmailScannerError):
        logger.error(f"{context}: {exception}" if context else str(exception))
        return exception
    
    # Convert common exceptions to EmailScannerError
    if isinstance(exception, (ConnectionError, TimeoutError)):
        error = TemporaryDatabaseError(
            f"Connection error: {str(exception)}",
            details={'original_exception': type(exception).__name__}
        )
    elif isinstance(exception, ValueError):
        error = ValidationError(
            f"Validation error: {str(exception)}",
            details={'original_exception': type(exception).__name__}
        )
    elif isinstance(exception, FileNotFoundError):
        error = ProcessingError(
            f"File not found: {str(exception)}",
            details={'original_exception': type(exception).__name__}
        )
    else:
        error = EmailScannerError(
            f"Unexpected error: {str(exception)}",
            error_code="UNKNOWN_ERROR",
            details={'original_exception': type(exception).__name__}
        )
    
    logger.error(f"{context}: {error}" if context else str(error))
    return error
