# Email Categorization and Analysis System

This application processes emails from a folder, categorizes them, identifies business entities, and stores the results in a vector database for similarity search.

## Features

- Reads `.eml` files from a specified folder
- Uses vector similarity search to identify duplicate/similar email templates
- Categorizes emails into: business, social, marketing, personal, shopping, transaction
- Extracts business entity names from emails
- Anonymizes emails to remove PII before storing as templates
- Stores processed email templates in a vector database (ChromaDB)
- Uses Ollama for LLM inference and embeddings generation

## Prerequisites

- Python 3.10 or higher
- Ollama installed locally (https://ollama.ai/download)
- Internet connection (for initial model downloads)

## Setup Instructions

1. Clone this repository:
   ```
   git clone <repository-url>
   cd email-categorization-system
   ```

2. Create and activate a virtual environment:
   ```
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create directories for emails and data:
   ```
   mkdir -p emails data
   ```

5. Place your `.eml` files in the `emails` directory:
   ```
   cp /path/to/your/emails/*.eml ./emails/
   ```

6. Start Ollama service locally (in a separate terminal):
   ```
   ollama serve
   ```

7. Pull the required models:
   ```
   ollama pull llama3
   ollama pull nomic-embed-text
   ```

8. Run the application:
   ```
   python app.py
   ```

## Configuration

You can configure the application using environment variables:

- `EMAIL_FOLDER`: Path to the folder containing email files (default: `./emails`)
- `DB_PATH`: Path to store the vector database (default: `./data`)

Example:
```
# On Windows
set EMAIL_FOLDER=C:\path\to\emails
set DB_PATH=C:\path\to\data
python app.py

# On macOS/Linux
EMAIL_FOLDER=/path/to/emails DB_PATH=/path/to/data python app.py
```

## Project Structure

- `app.py`: Main application code
- `requirements.txt`: Python dependencies
- `emails/`: Directory for email files
- `data/`: Directory for persistent vector database storage

## How It Works

1. The application scans the email folder for `.eml` files
2. For each email:
   - It extracts content and categorizes the email
   - It identifies the business entity from the email
   - It anonymizes the email to remove personal information
   - It generates an embedding for the anonymized template
3. It checks if a similar email template exists in the vector database
4. If no similar template is found:
   - The anonymized template, category, and business entity are stored in the vector database
5. If a similar template is found, the email is skipped
6. Detailed output is provided for each email, showing:
   - Filename
   - Category
   - Business entity
   - Processing status (new template, duplicate, or similar)

## Privacy and Security

- The system anonymizes all emails before storing them in the vector database
- Personal identifiable information (PII) is removed using both pattern matching and LLM-based anonymization
- Original email files remain untouched in the emails directory
- Only generalized templates are stored in the vector database

## Troubleshooting

- If you encounter connection issues with Ollama, ensure the Ollama service is running:
  ```
  # Check if Ollama is running
  curl http://localhost:11434/api/version
  ```

- To reset the database:
  ```
  rm -rf ./data/*
  ```

- If you get import errors, make sure your virtual environment is activated and all dependencies are installed.
