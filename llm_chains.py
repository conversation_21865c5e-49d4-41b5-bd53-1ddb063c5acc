"""LLM chain definitions for email processing."""
import logging
from typing import List, Optional, Dict, Any

from langchain_ollama import OllamaEmbeddings, OllamaLLM
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

# Configure logger
logger = logging.getLogger(__name__)

class LLMChainFactory:
    """Factory for creating LLM chains for email processing tasks."""
    
    @staticmethod
    def create_embeddings() -> OllamaEmbeddings:
        """Create Ollama embeddings for vector storage.
        
        Returns:
            OllamaEmbeddings instance configured for text embedding
            
        Raises:
            ConnectionError: If unable to connect to Ollama service
        """
        logger.info("Creating Ollama embeddings")
        try:
            embeddings = OllamaEmbeddings(
                model="nomic-embed-text",
                temperature=0.0
            )
            logger.info("Ollama embeddings created successfully")
            return embeddings
        except Exception as e:
            logger.error(f"Failed to create Ollama embeddings: {str(e)}")
            raise ConnectionError(f"Failed to connect to Ollama service: {str(e)}")
    
    @staticmethod
    def create_llm() -> OllamaLLM:
        """Create Ollama LLM for text generation.
        
        Returns:
            OllamaLLM instance configured for text generation
            
        Raises:
            ConnectionError: If unable to connect to Ollama service
        """
        logger.info("Creating Ollama LLM")
        try:
            llm = OllamaLLM(model="llama3")
            logger.info("Ollama LLM created successfully")
            return llm
        except Exception as e:
            logger.error(f"Failed to create Ollama LLM: {str(e)}")
            raise ConnectionError(f"Failed to connect to Ollama service: {str(e)}")
    
    @staticmethod
    def create_categorization_chain(llm: OllamaLLM, categories: Optional[List[str]] = None) -> Any:
        """Create chain for email categorization.
        
        Args:
            llm: The LLM to use for categorization
            categories: Optional list of categories to use for classification.
                        If None, defaults to standard categories.
        
        Returns:
            LangChain chain for email categorization
        """
        logger.info("Creating categorization chain")
        
        if not categories:
            categories = ["business", "social", "marketing", "personal", "shopping", "transaction"]
        
        categories_str = ", ".join(categories)
        logger.info(f"Using categories: {categories_str}")
        
        prompt = PromptTemplate(
            input_variables=["subject", "body", "categories"],
            template="""
            Categorize this email into one of these categories: {categories}.
            
            Email:
            Subject: {subject}
            
            Body: {body}
            
            Return only the category name.
            """
        )
        
        return lambda inputs: prompt.format(
            subject=inputs["subject"],
            body=inputs["body"],
            categories=categories_str
        ) | llm
    
    @staticmethod
    def create_entity_extraction_chain(llm: OllamaLLM) -> Any:
        """Create chain for business entity extraction.
        
        Args:
            llm: The LLM to use for entity extraction
        
        Returns:
            LangChain chain for business entity extraction
        """
        logger.info("Creating entity extraction chain")
        
        prompt = PromptTemplate(
            input_variables=["from_field", "subject"],
            template="""
            Extract the business entity or organization name from this email:
            From: {from_field}
            Subject: {subject}
            
            Return only the name of the business or organization. If none, return "Individual".
            """
        )
        
        return prompt | llm
    
    @staticmethod
    def create_anonymization_chain(llm: OllamaLLM) -> Any:
        """Create chain for email anonymization.
        
        Args:
            llm: The LLM to use for anonymization
        
        Returns:
            LangChain chain for email anonymization
        """
        logger.info("Creating anonymization chain")
        
        prompt = PromptTemplate(
            input_variables=["subject", "body"],
            template="""
            Create a generalized template version of this email by removing all personally identifiable information (PII) 
            such as names, addresses, phone numbers, specific dates, account numbers, and other personal details.
            Replace them with generic placeholders like [NAME], [ADDRESS], [PHONE], [DATE], etc.
            
            Email:
            Subject: {subject}
            
            Body: {body}
            
            Return only the anonymized template version that preserves the structure and purpose of the email.
            """
        )
        
        return prompt | llm
