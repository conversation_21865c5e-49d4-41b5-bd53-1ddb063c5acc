#!/usr/bin/env python3
"""Initialize the database with schema and sample data."""

import argparse
import logging
from config import set_environment
from database import initialize_database, get_db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Initialize database."""
    parser = argparse.ArgumentParser(description='Initialize database with schema')
    parser.add_argument('--environment', type=str, default='local',
                        help='Environment (local, development, production)')
    parser.add_argument('--create-sample-user', type=str, default=None,
                        help='Create a sample user with the given email')
    
    args = parser.parse_args()
    
    # Set environment
    set_environment(args.environment)
    
    try:
        # Initialize database
        logger.info(f"Initializing database for environment: {args.environment}")
        initialize_database()
        logger.info("Database initialized successfully")
        
        # Create sample user if requested
        if args.create_sample_user:
            db = get_db_manager()
            user = db.get_or_create_user(args.create_sample_user, "Sample User")
            logger.info(f"Created sample user: {user.email} (ID: {user.id})")
        
        # Show database status
        db = get_db_manager()
        
        # Count records in each table
        tables = ['users', 'business_entities', 'email_categories', 'user_business_interactions']
        for table in tables:
            try:
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                result = db.execute_query(count_query)
                count = result[0]['count'] if result else 0
                logger.info(f"{table}: {count} records")
            except Exception as e:
                logger.warning(f"Could not count records in {table}: {e}")
        
        logger.info("Database initialization complete")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

if __name__ == "__main__":
    main()
