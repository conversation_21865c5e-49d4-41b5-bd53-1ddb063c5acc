# Email Categorization System - Usage Guide

## Quick Start

### 1. Setup and Initialization

```bash
# Start the Docker services
docker-compose up -d

# Initialize the database
python init_database.py --environment development --create-sample-user <EMAIL>

# Verify services are running
docker-compose ps
```

### 2. Queue Emails for Processing

```bash
# Queue emails for a specific user
python generate_email_queue.py --user-email <EMAIL> --emails-directory ./emails

# Queue with priority
python generate_email_queue.py --user-email <EMAIL> --priority 1
```

### 3. Process Emails

The worker processes will automatically pick up queued emails:

```bash
# Workers are started automatically with docker-compose
# Or run manually:
python main.py
```

### 4. Analyze Results

```bash
# List email categories
python cli.py --user-email <EMAIL> --list-categories

# List business relationships
python cli.py --user-email <EMAIL> --list-entities

# List recent interactions
python cli.py --user-email <EMAIL> --list-interactions

# Process a single email file
python cli.py --user-email <EMAIL> --single-file ./emails/sample.eml
```

## Configuration

### Environment Files

The system supports three environments:

- **Local** (`.env.local`): For local development without Docker
- **Development** (`.env.development`): For Docker development environment
- **Production**: Uses environment variables from deployment

### Key Configuration Options

```bash
# Database
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=email_db
POSTGRES_USER=admin
POSTGRES_PASSWORD=admin123

# Redis Queue
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_QUEUE_NAME=email_queue

# ChromaDB Vector Store
CHROMADB_HOST=chromadb
CHROMADB_PORT=8000

# Ollama LLM
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
OLLAMA_LLM_MODEL=llama3

# Processing
NUM_WORKERS=4
BATCH_SIZE=20
SIMILARITY_THRESHOLD=0.8
```

## Database Schema

### Core Tables

1. **users**: User information and email addresses
2. **business_entities**: Business profiles with domain, industry, contact info
3. **email_categories**: Standardized email categories
4. **user_business_interactions**: Individual email interactions
5. **business_interaction_summary**: Aggregated interaction statistics

### Key Relationships

- Users have many business interactions
- Business entities have many user interactions
- Interactions are categorized and timestamped
- Summary tables provide quick analytics

## API Usage Examples

### Python API

```python
from processor import EmailProcessor
from config import set_environment
from database import initialize_database

# Set environment
set_environment('development')

# Initialize database
initialize_database()

# Create processor for a user
processor = EmailProcessor(user_email='<EMAIL>')

# Process a single email
result = processor.process_email('./emails/sample.eml')
print(f"Category: {result.category}")
print(f"Business: {result.business_entity}")
print(f"Status: {result.status}")
```

### Business Analytics

```python
from business_manager import get_business_manager
from database import get_db_manager

# Get business manager
bm = get_business_manager()
db = get_db_manager()

# Get user
user = db.get_user_by_email('<EMAIL>')

# Get business relationships
relationships = bm.get_user_business_relationships(user.id)

for rel in relationships:
    print(f"Business: {rel['business_name']}")
    print(f"Interactions: {rel['interaction_count']}")
    print(f"Category: {rel['most_common_category']}")
```

## Queue Management

### Queue Item Structure

```json
{
  "user_id": 1,
  "email_path": "/app/emails/sample.eml",
  "priority": 0
}
```

### Redis Queue Operations

```python
import redis
import json
from models import QueueItem

# Connect to Redis
redis_client = redis.Redis(host='redis', port=6379, decode_responses=True)

# Add item to queue
queue_item = QueueItem(user_id=1, email_path='/path/to/email.eml')
redis_client.rpush('email_queue', json.dumps(queue_item.dict()))

# Check queue length
queue_length = redis_client.llen('email_queue')
print(f"Queue length: {queue_length}")
```

## Monitoring and Debugging

### Service Health Checks

```bash
# Check all services
docker-compose ps

# Check logs
docker-compose logs email-categorization-app
docker-compose logs postgres
docker-compose logs redis
docker-compose logs chromadb

# Check queue status
redis-cli -h localhost -p 6379 llen email_queue
```

### Database Queries

```sql
-- Check user interactions
SELECT u.email, COUNT(*) as interaction_count
FROM users u
JOIN user_business_interactions ubi ON u.id = ubi.user_id
GROUP BY u.id, u.email;

-- Top businesses by interaction count
SELECT be.name, COUNT(*) as total_interactions
FROM business_entities be
JOIN user_business_interactions ubi ON be.id = ubi.business_entity_id
GROUP BY be.id, be.name
ORDER BY total_interactions DESC;

-- Recent interactions
SELECT u.email, be.name, ec.name as category, ubi.interaction_date
FROM user_business_interactions ubi
JOIN users u ON ubi.user_id = u.id
JOIN business_entities be ON ubi.business_entity_id = be.id
LEFT JOIN email_categories ec ON ubi.email_category_id = ec.id
ORDER BY ubi.interaction_date DESC
LIMIT 10;
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL container is running
   - Verify connection parameters in config
   - Check database initialization

2. **ChromaDB Connection Failed**
   - Ensure ChromaDB container is running
   - Check port 8000 is accessible
   - Verify ChromaDB service health

3. **Queue Not Processing**
   - Check Redis connection
   - Verify worker processes are running
   - Check queue name configuration

4. **Ollama Model Not Found**
   - Ensure Ollama container has downloaded models
   - Check model names in configuration
   - Verify Ollama service is accessible

### Performance Tuning

- Adjust `NUM_WORKERS` based on CPU cores
- Increase `BATCH_SIZE` for higher throughput
- Tune `SIMILARITY_THRESHOLD` for accuracy vs speed
- Monitor database connection pool size
