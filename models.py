"""Data models for the email categorization system."""
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

class EmailData(BaseModel):
    """Model representing extracted email data."""
    subject: str
    body: str
    from_field: str = ""
    to: str = ""
    date: str = ""

class ProcessingResult(BaseModel):
    """Model representing the result of email processing."""
    category: str
    business_entity: str
    template: str
    similarity_score: Optional[float] = None
    matched_with: Optional[str] = None
    status: str