"""Data models for the email categorization system."""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel

class EmailData(BaseModel):
    """Model representing extracted email data."""
    subject: str
    body: str
    from_field: str = ""
    to: str = ""
    date: str = ""

class ProcessingResult(BaseModel):
    """Model representing the result of email processing."""
    category: str
    business_entity: str
    template: str
    similarity_score: Optional[float] = None
    matched_with: Optional[str] = None
    status: str

class QueueItem(BaseModel):
    """Model representing an item in the Redis queue."""
    user_id: int
    email_path: str
    priority: int = 0

class User(BaseModel):
    """Model representing a user."""
    id: Optional[int] = None
    email: str
    name: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class BusinessEntity(BaseModel):
    """Model representing a business entity."""
    id: Optional[int] = None
    name: str
    domain: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None
    contact_email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class EmailCategory(BaseModel):
    """Model representing an email category."""
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None

class UserBusinessInteraction(BaseModel):
    """Model representing a user-business interaction."""
    id: Optional[int] = None
    user_id: int
    business_entity_id: int
    email_category_id: Optional[int] = None
    email_subject: Optional[str] = None
    email_from: Optional[str] = None
    email_date: Optional[datetime] = None
    interaction_date: Optional[datetime] = None
    email_file_path: Optional[str] = None
    template_id: Optional[str] = None
    similarity_score: Optional[float] = None
    processing_status: str = "processed"
    created_at: Optional[datetime] = None

class BusinessInteractionSummary(BaseModel):
    """Model representing business interaction summary."""
    id: Optional[int] = None
    user_id: int
    business_entity_id: int
    total_interactions: int = 0
    last_interaction_date: Optional[datetime] = None
    most_common_category_id: Optional[int] = None
    first_interaction_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None