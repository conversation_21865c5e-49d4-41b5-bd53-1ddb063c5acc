"""Main application module for email categorization system."""
import os
import argparse
from pathlib import Path

from .processor import EmailProcessor

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Email Categorization and Template Extraction')
    parser.add_argument('--email-folder', type=str, default='emails',
                        help='Folder containing email files (.eml)')
    parser.add_argument('--db-path', type=str, default='vectordb',
                        help='Path to store the vector database')
    return parser.parse_args()

def main():
    """Main entry point for the application."""
    args = parse_arguments()
    
    # Create processor
    processor = EmailProcessor(
        email_folder=args.email_folder,
        db_path=args.db_path
    )
    
    # Process emails
    processor.process_emails()

if __name__ == "__main__":
    main()
