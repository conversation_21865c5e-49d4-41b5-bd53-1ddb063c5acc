"""Main application module for email categorization system."""
import argparse

from .processor import EmailProcessor

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Email Categorization and Template Extraction')
    parser.add_argument('--email-folder', type=str, default='emails',
                        help='Folder containing email files (.eml)')
    parser.add_argument('--template-output-dir', type=str, default='processed_email_templates',
                        help='Directory to save processed templates')
    parser.add_argument('--chromadb-host', type=str, default=None,
                        help='ChromaDB server host (uses CHROMADB_HOST env var if not provided)')
    parser.add_argument('--chromadb-port', type=int, default=None,
                        help='ChromaDB server port (uses CHROMADB_PORT env var if not provided)')
    return parser.parse_args()

def main():
    """Main entry point for the application."""
    args = parse_arguments()

    # Create processor
    processor = EmailProcessor(
        email_folder=args.email_folder,
        template_output_dir=args.template_output_dir,
        chromadb_host=args.chromadb_host,
        chromadb_port=args.chromadb_port
    )

    # Process emails
    processor.process_emails()

if __name__ == "__main__":
    main()
