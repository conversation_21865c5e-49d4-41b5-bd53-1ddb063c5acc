"""Main application module for email categorization system."""
import argparse

from processor import EmailProcessor
from config import set_environment
from database import initialize_database

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Email Categorization and Template Extraction')
    parser.add_argument('--user-email', type=str, required=True,
                        help='Email address of the user')
    parser.add_argument('--email-folder', type=str, default=None,
                        help='Folder containing email files (.eml) (uses config if not provided)')
    parser.add_argument('--template-output-dir', type=str, default=None,
                        help='Directory to save processed templates (uses config if not provided)')
    parser.add_argument('--environment', type=str, default=None,
                        help='Environment (local, development, production)')
    return parser.parse_args()

def main():
    """Main entry point for the application."""
    args = parse_arguments()

    # Set environment if provided
    if args.environment:
        set_environment(args.environment)

    # Initialize database
    initialize_database()

    # Create processor
    processor = EmailProcessor(
        user_email=args.user_email,
        email_folder=args.email_folder,
        template_output_dir=args.template_output_dir
    )

    # Process emails
    processor.process_emails()

if __name__ == "__main__":
    main()
