"""SQLAlchemy database models for the email scanner application."""

import logging
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Float, ForeignKey,
    Boolean, Index, UniqueConstraint, func
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session

from ..utils.exceptions import DatabaseError

logger = logging.getLogger(__name__)

Base = declarative_base()


class TimestampMixin:
    """Mixin for adding timestamp columns."""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class User(Base, TimestampMixin):
    """User model for storing user information."""
    
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=True)
    
    # Relationships
    interactions = relationship("UserBusinessInteraction", back_populates="user", cascade="all, delete-orphan")
    summaries = relationship("BusinessInteractionSummary", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}')>"
    
    @classmethod
    def get_by_email(cls, session: Session, email: str) -> Optional['User']:
        """Get user by email address."""
        try:
            return session.query(cls).filter(cls.email == email).first()
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            raise DatabaseError(f"Error getting user by email: {e}", operation="get_by_email", table="users")
    
    @classmethod
    def create(cls, session: Session, email: str, name: str = None) -> 'User':
        """Create a new user."""
        try:
            user = cls(email=email, name=name)
            session.add(user)
            session.commit()
            session.refresh(user)
            logger.info(f"Created user: {email} (ID: {user.id})")
            return user
        except Exception as e:
            session.rollback()
            logger.error(f"Error creating user {email}: {e}")
            raise DatabaseError(f"Error creating user: {e}", operation="create", table="users")


class BusinessEntity(Base, TimestampMixin):
    """Business entity model for storing business information."""
    
    __tablename__ = 'business_entities'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), unique=True, nullable=False, index=True)
    domain = Column(String(255), nullable=True, index=True)
    industry = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    website = Column(String(255), nullable=True)
    contact_email = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    address = Column(Text, nullable=True)
    
    # Relationships
    interactions = relationship("UserBusinessInteraction", back_populates="business_entity", cascade="all, delete-orphan")
    summaries = relationship("BusinessInteractionSummary", back_populates="business_entity", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<BusinessEntity(id={self.id}, name='{self.name}')>"
    
    @classmethod
    def get_by_name(cls, session: Session, name: str) -> Optional['BusinessEntity']:
        """Get business entity by name."""
        try:
            return session.query(cls).filter(cls.name == name).first()
        except Exception as e:
            logger.error(f"Error getting business entity by name {name}: {e}")
            raise DatabaseError(f"Error getting business entity by name: {e}", operation="get_by_name", table="business_entities")
    
    @classmethod
    def create(cls, session: Session, name: str, **kwargs) -> 'BusinessEntity':
        """Create a new business entity."""
        try:
            business = cls(name=name, **kwargs)
            session.add(business)
            session.commit()
            session.refresh(business)
            logger.info(f"Created business entity: {name} (ID: {business.id})")
            return business
        except Exception as e:
            session.rollback()
            logger.error(f"Error creating business entity {name}: {e}")
            raise DatabaseError(f"Error creating business entity: {e}", operation="create", table="business_entities")


class EmailCategory(Base, TimestampMixin):
    """Email category model for standardized categorization."""
    
    __tablename__ = 'email_categories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Relationships
    interactions = relationship("UserBusinessInteraction", back_populates="email_category")
    
    def __repr__(self):
        return f"<EmailCategory(id={self.id}, name='{self.name}')>"
    
    @classmethod
    def get_by_name(cls, session: Session, name: str) -> Optional['EmailCategory']:
        """Get email category by name."""
        try:
            return session.query(cls).filter(cls.name == name).first()
        except Exception as e:
            logger.error(f"Error getting email category by name {name}: {e}")
            raise DatabaseError(f"Error getting email category by name: {e}", operation="get_by_name", table="email_categories")
    
    @classmethod
    def get_all(cls, session: Session) -> List['EmailCategory']:
        """Get all email categories."""
        try:
            return session.query(cls).order_by(cls.name).all()
        except Exception as e:
            logger.error(f"Error getting all email categories: {e}")
            raise DatabaseError(f"Error getting all email categories: {e}", operation="get_all", table="email_categories")


class UserBusinessInteraction(Base, TimestampMixin):
    """Model for tracking user-business interactions."""
    
    __tablename__ = 'user_business_interactions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    business_entity_id = Column(Integer, ForeignKey('business_entities.id', ondelete='CASCADE'), nullable=False, index=True)
    email_category_id = Column(Integer, ForeignKey('email_categories.id'), nullable=True)
    email_subject = Column(String(500), nullable=True)
    email_from = Column(String(255), nullable=True)
    email_date = Column(DateTime, nullable=True)
    interaction_date = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    email_file_path = Column(String(500), nullable=True)
    template_id = Column(String(100), nullable=True)
    similarity_score = Column(Float, nullable=True)
    processing_status = Column(String(50), default='processed', nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="interactions")
    business_entity = relationship("BusinessEntity", back_populates="interactions")
    email_category = relationship("EmailCategory", back_populates="interactions")
    
    # Indexes
    __table_args__ = (
        Index('idx_user_business_date', 'user_id', 'business_entity_id', 'interaction_date'),
        Index('idx_interaction_date', 'interaction_date'),
    )
    
    def __repr__(self):
        return f"<UserBusinessInteraction(id={self.id}, user_id={self.user_id}, business_id={self.business_entity_id})>"
    
    @classmethod
    def create(cls, session: Session, **kwargs) -> 'UserBusinessInteraction':
        """Create a new user-business interaction."""
        try:
            interaction = cls(**kwargs)
            session.add(interaction)
            session.commit()
            session.refresh(interaction)
            logger.info(f"Created interaction: user_id={interaction.user_id}, business_id={interaction.business_entity_id}")
            return interaction
        except Exception as e:
            session.rollback()
            logger.error(f"Error creating interaction: {e}")
            raise DatabaseError(f"Error creating interaction: {e}", operation="create", table="user_business_interactions")
    
    @classmethod
    def get_user_interactions(cls, session: Session, user_id: int, limit: int = 100) -> List['UserBusinessInteraction']:
        """Get recent user interactions."""
        try:
            return (session.query(cls)
                   .filter(cls.user_id == user_id)
                   .order_by(cls.interaction_date.desc())
                   .limit(limit)
                   .all())
        except Exception as e:
            logger.error(f"Error getting user interactions for user {user_id}: {e}")
            raise DatabaseError(f"Error getting user interactions: {e}", operation="get_user_interactions", table="user_business_interactions")


class BusinessInteractionSummary(Base, TimestampMixin):
    """Model for business interaction summary statistics."""
    
    __tablename__ = 'business_interaction_summary'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    business_entity_id = Column(Integer, ForeignKey('business_entities.id', ondelete='CASCADE'), nullable=False)
    total_interactions = Column(Integer, default=0, nullable=False)
    last_interaction_date = Column(DateTime, nullable=True)
    most_common_category_id = Column(Integer, ForeignKey('email_categories.id'), nullable=True)
    first_interaction_date = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="summaries")
    business_entity = relationship("BusinessEntity", back_populates="summaries")
    most_common_category = relationship("EmailCategory")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'business_entity_id', name='uq_user_business_summary'),
        Index('idx_user_business_summary', 'user_id', 'business_entity_id'),
    )
    
    def __repr__(self):
        return f"<BusinessInteractionSummary(user_id={self.user_id}, business_id={self.business_entity_id}, total={self.total_interactions})>"
    
    @classmethod
    def get_or_create(cls, session: Session, user_id: int, business_entity_id: int) -> 'BusinessInteractionSummary':
        """Get existing summary or create new one."""
        try:
            summary = session.query(cls).filter(
                cls.user_id == user_id,
                cls.business_entity_id == business_entity_id
            ).first()
            
            if not summary:
                summary = cls(user_id=user_id, business_entity_id=business_entity_id)
                session.add(summary)
                session.commit()
                session.refresh(summary)
                logger.info(f"Created business summary: user_id={user_id}, business_id={business_entity_id}")
            
            return summary
        except Exception as e:
            session.rollback()
            logger.error(f"Error getting/creating business summary: {e}")
            raise DatabaseError(f"Error getting/creating business summary: {e}", operation="get_or_create", table="business_interaction_summary")


class EmailProcessingQueue(Base, TimestampMixin):
    """Model for tracking email processing queue items."""
    
    __tablename__ = 'email_processing_queue'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    email_file_path = Column(String(500), nullable=False)
    status = Column(String(50), default='pending', nullable=False, index=True)
    priority = Column(Integer, default=0, nullable=False)
    attempts = Column(Integer, default=0, nullable=False)
    error_message = Column(Text, nullable=True)
    processed_at = Column(DateTime, nullable=True)
    job_id = Column(String(100), nullable=True, index=True)
    
    # Relationships
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index('idx_queue_status_priority', 'status', 'priority'),
        Index('idx_queue_user_status', 'user_id', 'status'),
    )
    
    def __repr__(self):
        return f"<EmailProcessingQueue(id={self.id}, user_id={self.user_id}, status='{self.status}')>"
