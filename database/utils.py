"""Database utility functions for initialization and management."""

import logging
import time
from typing import List

from .manager import get_db_manager, DatabaseManager
from ..utils.exceptions import DatabaseError

logger = logging.getLogger(__name__)


def initialize_database(max_retries: int = 5, retry_delay: int = 10) -> bool:
    """Initialize database with retry logic."""
    
    for attempt in range(max_retries):
        try:
            logger.info(f"Initializing database (attempt {attempt + 1}/{max_retries})")
            
            db_manager = get_db_manager()
            
            # Test connection first
            if not db_manager.test_connection():
                raise DatabaseError("Database connection test failed")
            
            # Create tables if they don't exist
            if not db_manager.verify_tables_exist():
                logger.info("Creating database tables...")
                db_manager.create_tables()
            
            # Verify tables exist after creation
            if not db_manager.verify_tables_exist():
                raise DatabaseError("Tables verification failed after creation")
            
            logger.info("Database initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database initialization attempt {attempt + 1} failed: {e}")
            
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                logger.error("Database initialization failed after all retries")
                raise DatabaseError(f"Database initialization failed: {e}")
    
    return False


def verify_tables_exist(required_tables: List[str] = None) -> bool:
    """Verify that all required tables exist."""
    
    if required_tables is None:
        required_tables = [
            'users', 
            'business_entities', 
            'email_categories',
            'user_business_interactions', 
            'business_interaction_summary',
            'email_processing_queue'
        ]
    
    try:
        db_manager = get_db_manager()
        return db_manager.verify_tables_exist()
    except Exception as e:
        logger.error(f"Error verifying tables: {e}")
        return False


def wait_for_database_ready(max_wait_time: int = 300, check_interval: int = 10) -> bool:
    """Wait for database to be ready with all tables created."""
    
    logger.info(f"Waiting for database to be ready (max {max_wait_time}s)...")
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            db_manager = get_db_manager()
            
            # Test connection
            if db_manager.test_connection():
                logger.info("Database connection successful")
                
                # Check if tables exist
                if db_manager.verify_tables_exist():
                    logger.info("All required tables exist")
                    return True
                else:
                    logger.info("Tables not ready yet, waiting...")
            else:
                logger.info("Database connection not ready, waiting...")
                
        except Exception as e:
            logger.warning(f"Database not ready: {e}")
        
        time.sleep(check_interval)
        elapsed = time.time() - start_time
        logger.info(f"Still waiting for database... ({elapsed:.1f}s elapsed)")
    
    logger.error(f"Database not ready after {max_wait_time} seconds")
    return False


def ensure_database_ready() -> None:
    """Ensure database is ready or raise an exception."""
    
    logger.info("Ensuring database is ready...")
    
    try:
        # First try to initialize normally
        if initialize_database():
            return
        
        # If that fails, wait for database to be ready
        if wait_for_database_ready():
            return
        
        # If still not ready, raise an error
        raise DatabaseError("Database is not ready after all attempts")
        
    except Exception as e:
        logger.error(f"Failed to ensure database is ready: {e}")
        raise DatabaseError(f"Failed to ensure database is ready: {e}")


def get_database_status() -> dict:
    """Get current database status information."""
    
    status = {
        'connected': False,
        'tables_exist': False,
        'error': None
    }
    
    try:
        db_manager = get_db_manager()
        
        # Test connection
        status['connected'] = db_manager.test_connection()
        
        if status['connected']:
            # Check tables
            status['tables_exist'] = db_manager.verify_tables_exist()
        
    except Exception as e:
        status['error'] = str(e)
        logger.error(f"Error getting database status: {e}")
    
    return status


def reset_database() -> bool:
    """Reset database by dropping and recreating all tables."""
    
    logger.warning("Resetting database - this will delete all data!")
    
    try:
        from .manager import reset_db_manager
        from .models import Base
        
        # Reset the manager to get a fresh connection
        reset_db_manager()
        db_manager = get_db_manager()
        
        # Drop all tables
        logger.info("Dropping all tables...")
        Base.metadata.drop_all(bind=db_manager.engine)
        
        # Recreate all tables
        logger.info("Creating all tables...")
        db_manager.create_tables()
        
        logger.info("Database reset completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        raise DatabaseError(f"Database reset failed: {e}")


def check_database_health() -> dict:
    """Perform comprehensive database health check."""
    
    health = {
        'status': 'healthy',
        'checks': {},
        'errors': []
    }
    
    try:
        db_manager = get_db_manager()
        
        # Connection test
        try:
            health['checks']['connection'] = db_manager.test_connection()
        except Exception as e:
            health['checks']['connection'] = False
            health['errors'].append(f"Connection failed: {e}")
        
        # Tables existence test
        try:
            health['checks']['tables'] = db_manager.verify_tables_exist()
        except Exception as e:
            health['checks']['tables'] = False
            health['errors'].append(f"Tables check failed: {e}")
        
        # Basic query test
        try:
            categories = db_manager.get_all_email_categories()
            health['checks']['basic_query'] = len(categories) > 0
            health['checks']['category_count'] = len(categories)
        except Exception as e:
            health['checks']['basic_query'] = False
            health['errors'].append(f"Basic query failed: {e}")
        
        # Determine overall status
        if not all([
            health['checks'].get('connection', False),
            health['checks'].get('tables', False),
            health['checks'].get('basic_query', False)
        ]):
            health['status'] = 'unhealthy'
        
    except Exception as e:
        health['status'] = 'error'
        health['errors'].append(f"Health check failed: {e}")
        logger.error(f"Database health check failed: {e}")
    
    return health
