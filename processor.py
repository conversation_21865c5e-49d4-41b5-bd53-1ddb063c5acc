"""Core email processing functionality."""
import os
import hashlib
import logging
import shutil
from typing import Dict, List, Any, Optional, Tuple

from models import EmailData, ProcessingResult
from extractors import EmailExtractor
from storage import VectorStore
from llm_chains import LLMChainFactory

# Configure logger
logger = logging.getLogger(__name__)

class EmailProcessor:
    """Processes emails for categorization and template extraction."""
    
    def __init__(
        self, 
        email_folder: str, 
        db_path: str,
        template_output_dir: str = "processed_email_templates",
        categories: Optional[List[str]] = None,
        use_spacy: bool = True
    ):
        """Initialize the email processor.
        
        Args:
            email_folder: Path to folder containing email files
            db_path: Path to vector database storage
            template_output_dir: Directory to save processed templates
            categories: Optional list of categories for classification
            use_spacy: Whether to use spaCy for NER-based anonymization
            
        Raises:
            ValueError: If directories cannot be created
            ConnectionError: If LLM services cannot be initialized
        """
        logger.info(f"Initializing EmailProcessor with email_folder={email_folder}, db_path={db_path}")
        self.email_folder = email_folder
        self.template_output_dir = template_output_dir
        self.categories = categories
        
        # Ensure directories exist
        try:
            os.makedirs(email_folder, exist_ok=True)
            os.makedirs(db_path, exist_ok=True)
            os.makedirs(template_output_dir, exist_ok=True)
            logger.info(f"Created output directories: {email_folder}, {db_path}, {template_output_dir}")
        except Exception as e:
            logger.error(f"Failed to create directories: {str(e)}")
            raise ValueError(f"Failed to create required directories: {str(e)}")
        
        try:
            # Initialize LLM components
            self.embeddings = LLMChainFactory.create_embeddings()
            self.llm = LLMChainFactory.create_llm()
            
            # Initialize chains
            self.categorization_chain = LLMChainFactory.create_categorization_chain(self.llm, categories)
            self.entity_extraction_chain = LLMChainFactory.create_entity_extraction_chain(self.llm)
            self.anonymization_chain = LLMChainFactory.create_anonymization_chain(self.llm)
            
            # Initialize vector store
            self.vector_store = VectorStore(db_path, self.embeddings)
            
            # Initialize extractor
            self.extractor = EmailExtractor(use_spacy=use_spacy)
            
            logger.info("EmailProcessor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EmailProcessor: {str(e)}")
            raise
    
    def create_anonymized_template(self, email_data: EmailData) -> str:
        """Create an anonymized template from email data using LLM.
        
        Args:
            email_data: The email data to anonymize
            
        Returns:
            Anonymized template string
            
        Raises:
            RuntimeError: If anonymization fails
        """
        logger.info("Creating anonymized template")
        
        try:
            # First apply basic regex and NER anonymization
            subject = self.extractor.anonymize_text(email_data.subject)
            body = self.extractor.anonymize_text(email_data.body[:1000])  # Limit body length
            
            logger.debug(f"Basic anonymization complete: subject='{subject[:30]}...'")
            
            # Then use LLM for more sophisticated anonymization
            try:
                result = self.anonymization_chain.invoke({
                    "subject": subject,
                    "body": body
                })
                logger.info("LLM anonymization successful")
                return result.strip()
            except Exception as e:
                logger.warning(f"Error during LLM anonymization: {e}")
                # Fallback to basic anonymization if LLM fails
                logger.info("Falling back to basic anonymization")
                return f"Subject: {subject}\n\nBody: {body[:500]}"
        except Exception as e:
            logger.error(f"Failed to create anonymized template: {str(e)}")
            raise RuntimeError(f"Failed to create anonymized template: {str(e)}")
    
    def categorize_email(self, email_data: EmailData) -> str:
        """Categorize email using LangChain LLM chain.
        
        Args:
            email_data: The email data to categorize
            
        Returns:
            Category string
            
        Raises:
            RuntimeError: If categorization fails
        """
        logger.info("Categorizing email")
        
        try:
            result = self.categorization_chain.invoke({
                "subject": email_data.subject,
                "body": email_data.body[:1000]  # Limit body length
            })
            category = result.strip()
            logger.info(f"Email categorized as: {category}")
            return category
        except Exception as e:
            logger.error(f"Failed to categorize email: {str(e)}")
            raise RuntimeError(f"Failed to categorize email: {str(e)}")
    
    def extract_business_entity(self, email_data: EmailData) -> str:
        """Extract business entity using LangChain LLM chain.
        
        Args:
            email_data: The email data to extract entity from
            
        Returns:
            Business entity string
            
        Raises:
            RuntimeError: If entity extraction fails
        """
        logger.info("Extracting business entity")
        
        try:
            result = self.entity_extraction_chain.invoke({
                "from_field": email_data.from_field,
                "subject": email_data.subject
            })
            entity = result.strip()
            logger.info(f"Extracted business entity: {entity}")
            return entity
        except Exception as e:
            logger.error(f"Failed to extract business entity: {str(e)}")
            raise RuntimeError(f"Failed to extract business entity: {str(e)}")
    
    def save_template_to_file(self, template: str, category: str, entity: str, original_filename: str) -> str:
        """Save the processed template to a file.
        
        Args:
            template: The anonymized template text
            category: The email category
            entity: The business entity
            original_filename: The original email filename
            
        Returns:
            Path to the saved template file
            
        Raises:
            IOError: If saving the template fails
        """
        logger.info(f"Saving template to file for {original_filename}")
        
        try:
            # Create a filename based on category, entity and original filename
            base_name = os.path.splitext(original_filename)[0]
            safe_entity = "".join(c if c.isalnum() else "_" for c in entity)
            template_filename = f"{category}_{safe_entity}_{base_name}.txt"
            template_path = os.path.join(self.template_output_dir, template_filename)
            
            # Save the template to file
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(template)
                
            logger.info(f"Template saved to {template_path}")
            return template_path
        except Exception as e:
            logger.error(f"Failed to save template to file: {str(e)}")
            raise IOError(f"Failed to save template to file: {str(e)}")
    
    def process_email(self, file_path: str) -> ProcessingResult:
        """Process a single email file.
        
        Args:
            file_path: Path to the email file
            
        Returns:
            ProcessingResult object with processing results
            
        Raises:
            Various exceptions depending on the processing stage
        """
        logger.info(f"Processing email: {file_path}")
        
        try:
            # Extract email content
            email_data = self.extractor.extract_email_content(file_path)
            
            # Categorize email
            category = self.categorize_email(email_data)
            
            # Extract business entity
            business_entity = self.extract_business_entity(email_data)
            
            # Create an anonymized template from the email
            anonymized_template = self.create_anonymized_template(email_data)
            
            # Generate ID for the document
            doc_id = hashlib.md5(anonymized_template.encode()).hexdigest()
            
            # Check if the document ID already exists in the collection
            if self.vector_store.check_duplicate(doc_id):
                return ProcessingResult(
                    category=category,
                    business_entity=business_entity,
                    template=anonymized_template,
                    status="Duplicate (exact match found)"
                )
            
            # Check for similar documents
            is_similar, similar_metadata, similarity_score = self.vector_store.find_similar(anonymized_template)
            
            if is_similar:
                return ProcessingResult(
                    category=category,
                    business_entity=business_entity,
                    template=anonymized_template,
                    similarity_score=similarity_score,
                    matched_with=similar_metadata.get('filename', 'unknown'),
                    status=f"Similar template found"
                )
            
            # Store in vector DB
            filename = os.path.basename(file_path)
            self.vector_store.add_document(
                text=anonymized_template,
                metadata={
                    "category": category,
                    "business_entity": business_entity,
                    "filename": filename,
                    "subject": email_data.subject,
                    "from": email_data.from_field,
                    "date": email_data.date
                },
                doc_id=doc_id
            )
            
            return ProcessingResult(
                category=category,
                business_entity=business_entity,
                template=anonymized_template,
                status="Processed (new template)"
            )
            
        except Exception as e:
            print(f"Error processing email {file_path}: {e}")
            return ProcessingResult(
                category="unknown",
                business_entity="unknown",
                template="",
                status=f"Error: {str(e)}"
            )
    
    def process_emails(self):
        """Process all emails in the specified folder."""
        # Check if email folder exists and is readable
        if not os.path.exists(self.email_folder):
            print(f"Error: Email folder '{self.email_folder}' does not exist.")
            return
        
        # Get list of email files
        try:
            email_files = [f for f in os.listdir(self.email_folder) if f.endswith('.eml')]
        except PermissionError:
            print(f"Error: Permission denied when accessing '{self.email_folder}'")
            return
        
        if not email_files:
            print(f"No .eml files found in '{self.email_folder}'")
            return
        
        print(f"Found {len(email_files)} email files to process")
        
        # Track processed and duplicate emails
        processed_count = 0
        duplicate_count = 0
        
        for filename in email_files:
            file_path = os.path.join(self.email_folder, filename)
            
            result = self.process_email(file_path)
            
            print(f"File: {filename}")
            print(f"Category: {result.category}")
            print(f"Business Entity: {result.business_entity}")
            print(f"Status: {result.status}")
            
            if result.matched_with:
                print(f"Matched with: {result.matched_with}")
                print(f"Similarity score: {result.similarity_score:.6f}")
            
            print("-" * 50)
            
            if "Duplicate" in result.status or "Similar" in result.status:
                duplicate_count += 1
            elif "Processed" in result.status:
                processed_count += 1
        
        print(f"Processing complete. {processed_count} new templates added, {duplicate_count} duplicates/similar found.")
