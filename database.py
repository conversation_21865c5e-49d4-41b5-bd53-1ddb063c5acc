"""Database connection and management for PostgreSQL."""
import os
import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.pool import SimpleConnectionPool
from contextlib import contextmanager

from models import (
    User, BusinessEntity, EmailCategory, UserBusinessInteraction, 
    BusinessInteractionSummary
)

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages PostgreSQL database connections and operations."""
    
    def __init__(self, 
                 host: str = None, 
                 port: int = None, 
                 database: str = None,
                 user: str = None, 
                 password: str = None,
                 min_connections: int = 1,
                 max_connections: int = 10):
        """Initialize database manager with connection parameters."""
        
        # Get connection parameters from environment if not provided
        self.host = host or os.getenv('POSTGRES_HOST', 'localhost')
        self.port = port or int(os.getenv('POSTGRES_PORT', '5432'))
        self.database = database or os.getenv('POSTGRES_DB', 'email_db')
        self.user = user or os.getenv('POSTGRES_USER', 'admin')
        self.password = password or os.getenv('POSTGRES_PASSWORD', 'admin123')
        
        # Connection pool
        self.pool = None
        self.min_connections = min_connections
        self.max_connections = max_connections
        
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize the connection pool."""
        try:
            self.pool = SimpleConnectionPool(
                self.min_connections,
                self.max_connections,
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.user,
                password=self.password,
                cursor_factory=RealDictCursor
            )
            logger.info(f"Database connection pool initialized: {self.host}:{self.port}/{self.database}")
        except Exception as e:
            logger.error(f"Failed to initialize database connection pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get a database connection from the pool."""
        conn = None
        try:
            conn = self.pool.getconn()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results."""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows."""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
    
    def execute_insert_returning(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """Execute an INSERT query with RETURNING clause."""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                conn.commit()
                return cursor.fetchone()
    
    # User management methods
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        query = "SELECT * FROM users WHERE email = %s"
        result = self.execute_query(query, (email,))
        if result:
            return User(**result[0])
        return None
    
    def create_user(self, email: str, name: str = None) -> User:
        """Create a new user."""
        query = """
            INSERT INTO users (email, name) 
            VALUES (%s, %s) 
            RETURNING *
        """
        result = self.execute_insert_returning(query, (email, name))
        return User(**result)
    
    def get_or_create_user(self, email: str, name: str = None) -> User:
        """Get existing user or create new one."""
        user = self.get_user_by_email(email)
        if not user:
            user = self.create_user(email, name)
        return user
    
    # Business entity management methods
    def get_business_entity_by_name(self, name: str) -> Optional[BusinessEntity]:
        """Get business entity by name."""
        query = "SELECT * FROM business_entities WHERE name = %s"
        result = self.execute_query(query, (name,))
        if result:
            return BusinessEntity(**result[0])
        return None
    
    def create_business_entity(self, name: str, domain: str = None, **kwargs) -> BusinessEntity:
        """Create a new business entity."""
        query = """
            INSERT INTO business_entities (name, domain, industry, description, website, contact_email, phone, address) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s) 
            RETURNING *
        """
        params = (
            name,
            domain,
            kwargs.get('industry'),
            kwargs.get('description'),
            kwargs.get('website'),
            kwargs.get('contact_email'),
            kwargs.get('phone'),
            kwargs.get('address')
        )
        result = self.execute_insert_returning(query, params)
        return BusinessEntity(**result)
    
    def get_or_create_business_entity(self, name: str, domain: str = None, **kwargs) -> BusinessEntity:
        """Get existing business entity or create new one."""
        business = self.get_business_entity_by_name(name)
        if not business:
            business = self.create_business_entity(name, domain, **kwargs)
        return business
    
    # Email category methods
    def get_email_category_by_name(self, name: str) -> Optional[EmailCategory]:
        """Get email category by name."""
        query = "SELECT * FROM email_categories WHERE name = %s"
        result = self.execute_query(query, (name,))
        if result:
            return EmailCategory(**result[0])
        return None
    
    def get_all_email_categories(self) -> List[EmailCategory]:
        """Get all email categories."""
        query = "SELECT * FROM email_categories ORDER BY name"
        results = self.execute_query(query)
        return [EmailCategory(**row) for row in results]
    
    # User-business interaction methods
    def create_user_business_interaction(self, 
                                       user_id: int,
                                       business_entity_id: int,
                                       email_category_id: int = None,
                                       email_subject: str = None,
                                       email_from: str = None,
                                       email_date: datetime = None,
                                       email_file_path: str = None,
                                       template_id: str = None,
                                       similarity_score: float = None) -> UserBusinessInteraction:
        """Create a new user-business interaction record."""
        query = """
            INSERT INTO user_business_interactions 
            (user_id, business_entity_id, email_category_id, email_subject, email_from, 
             email_date, email_file_path, template_id, similarity_score)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING *
        """
        params = (
            user_id, business_entity_id, email_category_id, email_subject,
            email_from, email_date, email_file_path, template_id, similarity_score
        )
        result = self.execute_insert_returning(query, params)
        return UserBusinessInteraction(**result)
    
    def get_business_interaction_summary(self, user_id: int, business_entity_id: int) -> Optional[BusinessInteractionSummary]:
        """Get business interaction summary for a user and business."""
        query = """
            SELECT * FROM business_interaction_summary 
            WHERE user_id = %s AND business_entity_id = %s
        """
        result = self.execute_query(query, (user_id, business_entity_id))
        if result:
            return BusinessInteractionSummary(**result[0])
        return None
    
    def get_user_business_interactions(self, user_id: int, limit: int = 100) -> List[UserBusinessInteraction]:
        """Get recent user-business interactions."""
        query = """
            SELECT * FROM user_business_interactions 
            WHERE user_id = %s 
            ORDER BY interaction_date DESC 
            LIMIT %s
        """
        results = self.execute_query(query, (user_id, limit))
        return [UserBusinessInteraction(**row) for row in results]
    
    def close(self):
        """Close all connections in the pool."""
        if self.pool:
            self.pool.closeall()
            logger.info("Database connection pool closed")

# Global database manager instance
db_manager = None

def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager

def initialize_database():
    """Initialize database with schema if needed."""
    db = get_db_manager()
    
    # Check if tables exist by trying to query users table
    try:
        db.execute_query("SELECT COUNT(*) FROM users LIMIT 1")
        logger.info("Database tables already exist")
    except Exception:
        logger.info("Database tables don't exist, creating schema...")
        # Read and execute schema file
        schema_path = os.path.join(os.path.dirname(__file__), 'database_schema.sql')
        if os.path.exists(schema_path):
            with open(schema_path, 'r') as f:
                schema_sql = f.read()
            
            # Execute schema creation
            with db.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(schema_sql)
                    conn.commit()
            logger.info("Database schema created successfully")
        else:
            logger.error("Database schema file not found")
            raise FileNotFoundError("database_schema.sql not found")
