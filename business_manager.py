"""Business profile management and interaction tracking."""
import logging
import re
from typing import Optional, List, Dict, Any
from datetime import datetime
from urllib.parse import urlparse

from models import BusinessEntity, User, UserBusinessInteraction, EmailData
from database import get_db_manager

logger = logging.getLogger(__name__)

class BusinessProfileManager:
    """Manages business profiles and user-business interactions."""
    
    def __init__(self):
        """Initialize the business profile manager."""
        self.db = get_db_manager()
    
    def extract_domain_from_email(self, email: str) -> Optional[str]:
        """Extract domain from email address."""
        if not email or '@' not in email:
            return None
        
        try:
            domain = email.split('@')[1].lower()
            # Remove common subdomains
            if domain.startswith('mail.'):
                domain = domain[5:]
            elif domain.startswith('smtp.'):
                domain = domain[5:]
            return domain
        except (IndexError, AttributeError):
            return None
    
    def extract_domain_from_url(self, url: str) -> Optional[str]:
        """Extract domain from URL."""
        if not url:
            return None
        
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Remove www prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            
            return domain
        except Exception:
            return None
    
    def normalize_business_name(self, name: str) -> str:
        """Normalize business name for consistent storage."""
        if not name:
            return ""
        
        # Remove common suffixes and normalize
        name = name.strip()
        
        # Remove email-like patterns
        if '@' in name:
            name = name.split('@')[0]
        
        # Remove common business suffixes for normalization
        suffixes = [
            r'\s+(inc\.?|incorporated)$',
            r'\s+(llc\.?)$',
            r'\s+(ltd\.?|limited)$',
            r'\s+(corp\.?|corporation)$',
            r'\s+(co\.?)$',
            r'\s+company$',
        ]
        
        for suffix in suffixes:
            name = re.sub(suffix, '', name, flags=re.IGNORECASE)
        
        return name.strip().title()
    
    def infer_industry_from_domain(self, domain: str) -> Optional[str]:
        """Infer industry from domain name (basic heuristics)."""
        if not domain:
            return None
        
        domain_lower = domain.lower()
        
        # Common industry patterns
        industry_patterns = {
            'technology': ['tech', 'software', 'app', 'digital', 'cloud', 'ai', 'data'],
            'finance': ['bank', 'finance', 'invest', 'credit', 'loan', 'pay', 'wallet'],
            'retail': ['shop', 'store', 'retail', 'market', 'buy', 'sell'],
            'healthcare': ['health', 'medical', 'care', 'hospital', 'clinic'],
            'education': ['edu', 'school', 'university', 'college', 'learn'],
            'media': ['news', 'media', 'blog', 'press', 'journal'],
            'travel': ['travel', 'hotel', 'flight', 'booking', 'trip'],
            'food': ['food', 'restaurant', 'delivery', 'kitchen', 'recipe'],
            'real_estate': ['real', 'estate', 'property', 'rent', 'home'],
            'automotive': ['auto', 'car', 'vehicle', 'drive', 'motor'],
        }
        
        for industry, keywords in industry_patterns.items():
            if any(keyword in domain_lower for keyword in keywords):
                return industry
        
        return 'other'
    
    def create_business_profile_from_email(self, 
                                         business_name: str, 
                                         email_data: EmailData) -> BusinessEntity:
        """Create a business profile from email data."""
        
        # Normalize business name
        normalized_name = self.normalize_business_name(business_name)
        
        # Extract domain from sender email
        domain = self.extract_domain_from_email(email_data.from_field)
        
        # Infer industry
        industry = self.infer_industry_from_domain(domain) if domain else None
        
        # Create business entity
        business_data = {
            'domain': domain,
            'industry': industry,
            'contact_email': email_data.from_field,
            'description': f"Business entity identified from email communication"
        }
        
        # Try to extract website from email body (basic pattern matching)
        if email_data.body:
            url_pattern = r'https?://(?:www\.)?([a-zA-Z0-9-]+\.[a-zA-Z]{2,})'
            urls = re.findall(url_pattern, email_data.body)
            if urls:
                # Use the first URL found as potential website
                potential_domain = urls[0].lower()
                if not domain or potential_domain != domain:
                    business_data['website'] = f"https://{potential_domain}"
        
        return self.db.create_business_entity(normalized_name, **business_data)
    
    def get_or_create_business_entity(self, 
                                    business_name: str, 
                                    email_data: EmailData) -> BusinessEntity:
        """Get existing business entity or create new one."""
        
        normalized_name = self.normalize_business_name(business_name)
        
        # Try to find existing business
        existing_business = self.db.get_business_entity_by_name(normalized_name)
        
        if existing_business:
            logger.info(f"Found existing business entity: {normalized_name}")
            return existing_business
        
        # Create new business entity
        logger.info(f"Creating new business entity: {normalized_name}")
        return self.create_business_profile_from_email(normalized_name, email_data)
    
    def record_user_business_interaction(self,
                                       user: User,
                                       business_entity: BusinessEntity,
                                       email_data: EmailData,
                                       category: str,
                                       email_file_path: str = None,
                                       template_id: str = None,
                                       similarity_score: float = None) -> UserBusinessInteraction:
        """Record a user-business interaction."""
        
        # Get email category ID
        email_category = self.db.get_email_category_by_name(category)
        email_category_id = email_category.id if email_category else None
        
        # Parse email date
        email_date = None
        if email_data.date:
            try:
                # Try to parse common email date formats
                from email.utils import parsedate_to_datetime
                email_date = parsedate_to_datetime(email_data.date)
            except Exception:
                logger.warning(f"Could not parse email date: {email_data.date}")
        
        # Create interaction record
        interaction = self.db.create_user_business_interaction(
            user_id=user.id,
            business_entity_id=business_entity.id,
            email_category_id=email_category_id,
            email_subject=email_data.subject[:500] if email_data.subject else None,
            email_from=email_data.from_field,
            email_date=email_date,
            email_file_path=email_file_path,
            template_id=template_id,
            similarity_score=similarity_score
        )
        
        logger.info(f"Recorded interaction between user {user.id} and business {business_entity.name}")
        return interaction
    
    def get_business_interaction_stats(self, user_id: int, business_entity_id: int) -> Dict[str, Any]:
        """Get interaction statistics for a user and business."""
        
        summary = self.db.get_business_interaction_summary(user_id, business_entity_id)
        
        if not summary:
            return {
                'total_interactions': 0,
                'first_interaction': None,
                'last_interaction': None,
                'most_common_category': None
            }
        
        # Get most common category name
        most_common_category = None
        if summary.most_common_category_id:
            categories = self.db.get_all_email_categories()
            for cat in categories:
                if cat.id == summary.most_common_category_id:
                    most_common_category = cat.name
                    break
        
        return {
            'total_interactions': summary.total_interactions,
            'first_interaction': summary.first_interaction_date,
            'last_interaction': summary.last_interaction_date,
            'most_common_category': most_common_category
        }
    
    def get_user_business_relationships(self, user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get all business relationships for a user with statistics."""
        
        interactions = self.db.get_user_business_interactions(user_id, limit=1000)
        
        # Group by business entity
        business_stats = {}
        for interaction in interactions:
            business_id = interaction.business_entity_id
            if business_id not in business_stats:
                business_stats[business_id] = {
                    'business_entity_id': business_id,
                    'interaction_count': 0,
                    'categories': {},
                    'first_interaction': interaction.interaction_date,
                    'last_interaction': interaction.interaction_date
                }
            
            stats = business_stats[business_id]
            stats['interaction_count'] += 1
            
            # Track categories
            if interaction.email_category_id:
                cat_id = interaction.email_category_id
                stats['categories'][cat_id] = stats['categories'].get(cat_id, 0) + 1
            
            # Update date ranges
            if interaction.interaction_date:
                if not stats['first_interaction'] or interaction.interaction_date < stats['first_interaction']:
                    stats['first_interaction'] = interaction.interaction_date
                if not stats['last_interaction'] or interaction.interaction_date > stats['last_interaction']:
                    stats['last_interaction'] = interaction.interaction_date
        
        # Convert to list and add business entity details
        result = []
        for business_id, stats in business_stats.items():
            # Get business entity details
            business_query = "SELECT * FROM business_entities WHERE id = %s"
            business_data = self.db.execute_query(business_query, (business_id,))
            
            if business_data:
                business = BusinessEntity(**business_data[0])
                stats['business_name'] = business.name
                stats['business_domain'] = business.domain
                stats['business_industry'] = business.industry
                
                # Find most common category
                if stats['categories']:
                    most_common_cat_id = max(stats['categories'], key=stats['categories'].get)
                    categories = self.db.get_all_email_categories()
                    for cat in categories:
                        if cat.id == most_common_cat_id:
                            stats['most_common_category'] = cat.name
                            break
                
                result.append(stats)
        
        # Sort by interaction count (descending)
        result.sort(key=lambda x: x['interaction_count'], reverse=True)
        
        return result[:limit]

# Global business manager instance
_business_manager = None

def get_business_manager() -> BusinessProfileManager:
    """Get the global business manager instance."""
    global _business_manager
    if _business_manager is None:
        _business_manager = BusinessProfileManager()
    return _business_manager
