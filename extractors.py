"""Email content extraction functionality."""
import email
import re
import os
import logging
from pathlib import Path
from email.parser import BytesParser
from email.policy import default
from typing import Dict, List, Any, Optional, Union

from models import EmailData

# Configure logger
logger = logging.getLogger(__name__)

class EmailExtractor:
    """Extracts content from email files and anonymizes sensitive information."""
    
    def __init__(self, use_spacy: bool = True):
        """Initialize the email extractor.
        
        Args:
            use_spacy: Whether to use spaCy for NER-based anonymization
        """
        self.use_spacy = use_spacy
        self.nlp = None
        
        if use_spacy:
            try:
                import spacy
                # Load the English model with NER capabilities
                logger.info("Loading spaCy model for NER-based anonymization")
                self.nlp = spacy.load("en_core_web_sm")
                logger.info("spaCy model loaded successfully")
            except ImportError:
                logger.warning("spaCy not installed. Falling back to regex-based anonymization only.")
                self.use_spacy = False
            except OSError:
                logger.warning("spaCy model not found. Run 'python -m spacy download en_core_web_sm'")
                self.use_spacy = False
    
    def extract_email_content(self, file_path: str) -> EmailData:
        """Extract content from an email file.
        
        Args:
            file_path: Path to the email file (.eml)
            
        Returns:
            EmailData object containing extracted email content
            
        Raises:
            FileNotFoundError: If the email file doesn't exist
            ValueError: If the file is not a valid email format
        """
        logger.info(f"Extracting content from email file: {file_path}")
        
        try:
            if not os.path.exists(file_path):
                logger.error(f"Email file not found: {file_path}")
                raise FileNotFoundError(f"Email file not found: {file_path}")
                
            with open(file_path, 'rb') as fp:
                try:
                    msg = BytesParser(policy=default).parse(fp)
                except Exception as e:
                    logger.error(f"Failed to parse email file {file_path}: {str(e)}")
                    raise ValueError(f"Invalid email format: {str(e)}")
            
            subject = msg.get('Subject', '')
            body = ""
            
            # Extract body based on content type
            if msg.is_multipart():
                for part in msg.iter_parts():
                    if part.get_content_type() == "text/plain":
                        body += part.get_content()
            else:
                body = msg.get_content()
                
            email_data = EmailData(
                subject=subject,
                body=body,
                from_field=msg.get('From', ''),
                to=msg.get('To', ''),
                date=msg.get('Date', '')
            )
            
            logger.debug(f"Successfully extracted email content: subject='{subject[:30]}...'")
            return email_data
            
        except Exception as e:
            logger.exception(f"Unexpected error extracting email content: {str(e)}")
            raise
    
    def anonymize_text(self, text: str) -> str:
        """Anonymize text to remove personally identifiable information.
        
        Uses a combination of regex patterns and spaCy NER (if enabled)
        to identify and replace sensitive information with placeholders.
        
        Args:
            text: The text to anonymize
            
        Returns:
            Anonymized text with PII replaced by placeholders
        """
        if not text:
            return ""
            
        logger.debug(f"Anonymizing text (length: {len(text)})")
        
        # First apply regex-based anonymization
        try:
            # Replace emails
            text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[EMAIL]', text)
            # Replace phone numbers
            text = re.sub(r'\b(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b', '[PHONE]', text)
            # Replace URLs
            text = re.sub(r'https?://\S+|www\.\S+', '[URL]', text)
            # Replace dates (simple pattern)
            text = re.sub(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', '[DATE]', text)
            # Replace addresses (simple pattern)
            text = re.sub(r'\b\d+\s+[A-Za-z\s]+(?:Avenue|Ave|Street|St|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr)\b', '[ADDRESS]', text)
            # Replace numbers that might be account numbers, SSNs, etc.
            text = re.sub(r'\b\d{4,}\b', '[NUMBER]', text)
        except Exception as e:
            logger.error(f"Error during regex anonymization: {str(e)}")
        
        # Then apply spaCy NER-based anonymization if enabled
        if self.use_spacy and self.nlp and text:
            try:
                doc = self.nlp(text)
                
                # Create a list of replacements to make
                replacements = []
                for ent in doc.ents:
                    if ent.label_ in ["PERSON", "ORG", "GPE", "LOC", "MONEY", "CARDINAL"]:
                        placeholder = f"[{ent.label_}]"
                        replacements.append((ent.start_char, ent.end_char, placeholder))
                
                # Apply replacements in reverse order to maintain correct indices
                for start, end, placeholder in sorted(replacements, reverse=True):
                    text = text[:start] + placeholder + text[end:]
                    
                logger.debug(f"Applied {len(replacements)} spaCy NER replacements")
                
            except Exception as e:
                logger.error(f"Error during spaCy NER anonymization: {str(e)}")
        
        return text
