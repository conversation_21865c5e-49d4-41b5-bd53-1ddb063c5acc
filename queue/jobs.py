"""RQ job definitions for email processing."""

import logging
import os
from typing import Dict, Any
from rq import get_current_job

from ..core.processor import EmailProcessor
from ..database.utils import ensure_database_ready
from ..config.settings import get_config
from ..config.logging_config import setup_logging
from ..utils.exceptions import ProcessingError, DatabaseError, handle_exception

logger = logging.getLogger(__name__)


def process_email_job(user_id: int, email_path: str) -> Dict[str, Any]:
    """
    RQ job function to process a single email.
    
    Args:
        user_id: ID of the user who owns the email
        email_path: Path to the email file to process
        
    Returns:
        Dictionary containing processing results
        
    Raises:
        ProcessingError: If email processing fails
        DatabaseError: If database operations fail
    """
    
    # Get current job for metadata
    job = get_current_job()
    job_id = job.id if job else "unknown"
    
    # Setup logging for the job
    config = get_config()
    setup_logging(config.log_level, config.log_file, config.environment)
    
    logger.info(f"Starting email processing job {job_id} for user {user_id}: {email_path}")
    
    try:
        # Ensure database is ready
        ensure_database_ready()
        
        # Verify email file exists
        if not os.path.exists(email_path):
            raise ProcessingError(
                f"Email file not found: {email_path}",
                email_path=email_path,
                stage="file_validation"
            )
        
        # Get user email from database
        from ..database.manager import get_db_manager
        db_manager = get_db_manager()
        
        # Get user by ID
        with db_manager.get_session() as session:
            user = session.query(db_manager.User).filter(db_manager.User.id == user_id).first()
            
        if not user:
            raise ProcessingError(
                f"User not found: {user_id}",
                email_path=email_path,
                stage="user_validation"
            )
        
        user_email = user.email
        logger.info(f"Processing email for user: {user_email}")
        
        # Initialize email processor
        processor = EmailProcessor(
            user_email=user_email,
            email_folder=config.directories.email_folder,
            template_output_dir=config.directories.template_output_dir
        )
        
        # Process the email
        result = processor.process_email(email_path)
        
        # Prepare job result
        job_result = {
            'job_id': job_id,
            'user_id': user_id,
            'user_email': user_email,
            'email_path': email_path,
            'status': 'completed',
            'processing_result': {
                'category': result.category,
                'business_entity': result.business_entity,
                'template': result.template[:500] + '...' if len(result.template) > 500 else result.template,
                'similarity_score': result.similarity_score,
                'matched_with': result.matched_with,
                'status': result.status
            }
        }
        
        logger.info(f"Email processing job {job_id} completed successfully: {result.status}")
        return job_result
        
    except Exception as e:
        # Handle and log the exception
        error = handle_exception(logger, e, f"Email processing job {job_id}")
        
        # Prepare error result
        error_result = {
            'job_id': job_id,
            'user_id': user_id,
            'email_path': email_path,
            'status': 'failed',
            'error': {
                'type': type(error).__name__,
                'message': str(error),
                'code': getattr(error, 'error_code', 'UNKNOWN_ERROR'),
                'details': getattr(error, 'details', {})
            }
        }
        
        logger.error(f"Email processing job {job_id} failed: {error}")
        
        # Re-raise the exception so RQ marks the job as failed
        raise error


def batch_process_emails_job(user_id: int, email_paths: list, batch_size: int = 10) -> Dict[str, Any]:
    """
    RQ job function to process multiple emails in batches.
    
    Args:
        user_id: ID of the user who owns the emails
        email_paths: List of email file paths to process
        batch_size: Number of emails to process in each batch
        
    Returns:
        Dictionary containing batch processing results
    """
    
    job = get_current_job()
    job_id = job.id if job else "unknown"
    
    # Setup logging
    config = get_config()
    setup_logging(config.log_level, config.log_file, config.environment)
    
    logger.info(f"Starting batch email processing job {job_id} for user {user_id}: {len(email_paths)} emails")
    
    results = {
        'job_id': job_id,
        'user_id': user_id,
        'total_emails': len(email_paths),
        'processed': 0,
        'failed': 0,
        'results': [],
        'errors': []
    }
    
    try:
        # Ensure database is ready
        ensure_database_ready()
        
        # Process emails in batches
        for i in range(0, len(email_paths), batch_size):
            batch = email_paths[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: {len(batch)} emails")
            
            for email_path in batch:
                try:
                    # Process individual email
                    email_result = process_email_job(user_id, email_path)
                    results['results'].append(email_result)
                    results['processed'] += 1
                    
                except Exception as e:
                    error_info = {
                        'email_path': email_path,
                        'error': str(e),
                        'type': type(e).__name__
                    }
                    results['errors'].append(error_info)
                    results['failed'] += 1
                    logger.error(f"Failed to process {email_path}: {e}")
        
        results['status'] = 'completed'
        logger.info(f"Batch processing job {job_id} completed: {results['processed']} processed, {results['failed']} failed")
        
    except Exception as e:
        results['status'] = 'failed'
        results['error'] = str(e)
        logger.error(f"Batch processing job {job_id} failed: {e}")
        raise
    
    return results


def cleanup_old_jobs_job(days_old: int = 7) -> Dict[str, Any]:
    """
    RQ job function to cleanup old completed and failed jobs.
    
    Args:
        days_old: Number of days after which jobs should be cleaned up
        
    Returns:
        Dictionary containing cleanup results
    """
    
    job = get_current_job()
    job_id = job.id if job else "unknown"
    
    # Setup logging
    config = get_config()
    setup_logging(config.log_level, config.log_file, config.environment)
    
    logger.info(f"Starting job cleanup job {job_id}: removing jobs older than {days_old} days")
    
    try:
        from ..queue.manager import get_queue_manager
        queue_manager = get_queue_manager()
        
        # This would need to be implemented based on RQ's cleanup capabilities
        # For now, just return a placeholder result
        
        result = {
            'job_id': job_id,
            'status': 'completed',
            'days_old': days_old,
            'cleaned_jobs': 0,
            'message': 'Job cleanup functionality not yet implemented'
        }
        
        logger.info(f"Job cleanup job {job_id} completed")
        return result
        
    except Exception as e:
        logger.error(f"Job cleanup job {job_id} failed: {e}")
        raise


def health_check_job() -> Dict[str, Any]:
    """
    RQ job function to perform system health checks.
    
    Returns:
        Dictionary containing health check results
    """
    
    job = get_current_job()
    job_id = job.id if job else "unknown"
    
    # Setup logging
    config = get_config()
    setup_logging(config.log_level, config.log_file, config.environment)
    
    logger.info(f"Starting health check job {job_id}")
    
    try:
        from ..database.utils import check_database_health
        from ..queue.manager import get_queue_manager
        
        # Check database health
        db_health = check_database_health()
        
        # Check queue health
        queue_manager = get_queue_manager()
        queue_health = queue_manager.health_check()
        
        # Overall health status
        overall_status = 'healthy'
        if db_health['status'] != 'healthy' or queue_health['status'] != 'healthy':
            overall_status = 'unhealthy'
        
        result = {
            'job_id': job_id,
            'status': 'completed',
            'timestamp': logger.info,
            'overall_status': overall_status,
            'database': db_health,
            'queue': queue_health
        }
        
        logger.info(f"Health check job {job_id} completed: {overall_status}")
        return result
        
    except Exception as e:
        logger.error(f"Health check job {job_id} failed: {e}")
        raise
