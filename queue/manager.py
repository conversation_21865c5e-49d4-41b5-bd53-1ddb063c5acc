"""RQ queue manager for email processing jobs."""

import logging
from typing import Optional, List, Dict, Any
import redis
from rq import Queue, Worker, Connection, Job
from rq.exceptions import NoSuchJobError

from ..config.settings import get_config
from ..utils.exceptions import QueueError, TemporaryQueueError

logger = logging.getLogger(__name__)


class QueueManager:
    """Manages RQ queues for email processing."""
    
    def __init__(self, redis_url: str = None):
        """Initialize queue manager."""
        config = get_config()
        self.redis_url = redis_url or config.redis.url
        self.queue_name = config.redis.queue_name
        self.default_timeout = config.redis.default_timeout
        self.result_ttl = config.redis.result_ttl
        
        try:
            self.redis_conn = redis.from_url(self.redis_url)
            self.queue = Queue(self.queue_name, connection=self.redis_conn)
            logger.info(f"QueueManager initialized with queue: {self.queue_name}")
        except Exception as e:
            logger.error(f"Failed to initialize QueueManager: {e}")
            raise QueueError(f"Failed to initialize QueueManager: {e}")
    
    def enqueue_email_processing(self, 
                                user_id: int, 
                                email_path: str, 
                                priority: int = 0,
                                timeout: int = None,
                                retry_attempts: int = 3) -> Job:
        """Enqueue an email processing job."""
        
        try:
            from .jobs import process_email_job
            
            job_timeout = timeout or self.default_timeout
            
            # Create job with metadata
            job = self.queue.enqueue(
                process_email_job,
                user_id=user_id,
                email_path=email_path,
                timeout=job_timeout,
                result_ttl=self.result_ttl,
                job_timeout=job_timeout,
                retry=retry_attempts > 0,
                meta={
                    'user_id': user_id,
                    'email_path': email_path,
                    'priority': priority,
                    'retry_attempts': retry_attempts
                }
            )
            
            logger.info(f"Enqueued email processing job {job.id} for user {user_id}: {email_path}")
            return job
            
        except Exception as e:
            logger.error(f"Failed to enqueue email processing job: {e}")
            raise QueueError(f"Failed to enqueue job: {e}", queue_name=self.queue_name)
    
    def get_job(self, job_id: str) -> Optional[Job]:
        """Get job by ID."""
        try:
            return Job.fetch(job_id, connection=self.redis_conn)
        except NoSuchJobError:
            logger.warning(f"Job {job_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error getting job {job_id}: {e}")
            raise QueueError(f"Error getting job: {e}", job_id=job_id)
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status and metadata."""
        try:
            job = self.get_job(job_id)
            if not job:
                return {'status': 'not_found', 'job_id': job_id}
            
            return {
                'job_id': job.id,
                'status': job.get_status(),
                'created_at': job.created_at,
                'started_at': job.started_at,
                'ended_at': job.ended_at,
                'result': job.result,
                'exc_info': job.exc_info,
                'meta': job.meta,
                'timeout': job.timeout,
                'retry_attempts': job.meta.get('retry_attempts', 0)
            }
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {e}")
            raise QueueError(f"Error getting job status: {e}", job_id=job_id)
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job."""
        try:
            job = self.get_job(job_id)
            if not job:
                logger.warning(f"Cannot cancel job {job_id}: not found")
                return False
            
            job.cancel()
            logger.info(f"Cancelled job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            raise QueueError(f"Error cancelling job: {e}", job_id=job_id)
    
    def retry_job(self, job_id: str) -> Optional[Job]:
        """Retry a failed job."""
        try:
            job = self.get_job(job_id)
            if not job:
                logger.warning(f"Cannot retry job {job_id}: not found")
                return None
            
            if job.get_status() != 'failed':
                logger.warning(f"Cannot retry job {job_id}: status is {job.get_status()}")
                return None
            
            # Get original job parameters
            meta = job.meta
            user_id = meta.get('user_id')
            email_path = meta.get('email_path')
            priority = meta.get('priority', 0)
            retry_attempts = meta.get('retry_attempts', 0)
            
            if retry_attempts <= 0:
                logger.warning(f"Cannot retry job {job_id}: no retry attempts remaining")
                return None
            
            # Create new job with decremented retry attempts
            new_job = self.enqueue_email_processing(
                user_id=user_id,
                email_path=email_path,
                priority=priority,
                retry_attempts=retry_attempts - 1
            )
            
            logger.info(f"Retried job {job_id} as new job {new_job.id}")
            return new_job
            
        except Exception as e:
            logger.error(f"Error retrying job {job_id}: {e}")
            raise QueueError(f"Error retrying job: {e}", job_id=job_id)
    
    def get_queue_info(self) -> Dict[str, Any]:
        """Get queue information and statistics."""
        try:
            return {
                'name': self.queue_name,
                'length': len(self.queue),
                'failed_count': self.queue.failed_job_registry.count,
                'started_count': self.queue.started_job_registry.count,
                'finished_count': self.queue.finished_job_registry.count,
                'scheduled_count': self.queue.scheduled_job_registry.count,
                'deferred_count': self.queue.deferred_job_registry.count,
                'workers': [worker.name for worker in Worker.all(connection=self.redis_conn)]
            }
        except Exception as e:
            logger.error(f"Error getting queue info: {e}")
            raise QueueError(f"Error getting queue info: {e}", queue_name=self.queue_name)
    
    def get_failed_jobs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get failed jobs with details."""
        try:
            failed_jobs = []
            for job in self.queue.failed_job_registry.get_job_ids()[:limit]:
                try:
                    job_obj = Job.fetch(job, connection=self.redis_conn)
                    failed_jobs.append({
                        'job_id': job_obj.id,
                        'created_at': job_obj.created_at,
                        'failed_at': job_obj.ended_at,
                        'exc_info': job_obj.exc_info,
                        'meta': job_obj.meta
                    })
                except Exception as e:
                    logger.warning(f"Error getting failed job {job}: {e}")
            
            return failed_jobs
        except Exception as e:
            logger.error(f"Error getting failed jobs: {e}")
            raise QueueError(f"Error getting failed jobs: {e}", queue_name=self.queue_name)
    
    def clear_failed_jobs(self) -> int:
        """Clear all failed jobs."""
        try:
            count = self.queue.failed_job_registry.count
            self.queue.failed_job_registry.requeue_all()
            logger.info(f"Cleared {count} failed jobs")
            return count
        except Exception as e:
            logger.error(f"Error clearing failed jobs: {e}")
            raise QueueError(f"Error clearing failed jobs: {e}", queue_name=self.queue_name)
    
    def get_workers(self) -> List[Dict[str, Any]]:
        """Get information about active workers."""
        try:
            workers = []
            for worker in Worker.all(connection=self.redis_conn):
                workers.append({
                    'name': worker.name,
                    'state': worker.get_state(),
                    'current_job': worker.get_current_job_id(),
                    'successful_jobs': worker.successful_job_count,
                    'failed_jobs': worker.failed_job_count,
                    'total_working_time': worker.total_working_time,
                    'birth_date': worker.birth_date,
                    'last_heartbeat': worker.last_heartbeat
                })
            return workers
        except Exception as e:
            logger.error(f"Error getting workers: {e}")
            raise QueueError(f"Error getting workers: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on queue system."""
        health = {
            'status': 'healthy',
            'redis_connected': False,
            'queue_accessible': False,
            'workers_active': 0,
            'errors': []
        }
        
        try:
            # Test Redis connection
            self.redis_conn.ping()
            health['redis_connected'] = True
            
            # Test queue access
            queue_info = self.get_queue_info()
            health['queue_accessible'] = True
            health['queue_length'] = queue_info['length']
            health['workers_active'] = len(queue_info['workers'])
            
            # Check if workers are active
            if health['workers_active'] == 0:
                health['status'] = 'warning'
                health['errors'].append('No active workers found')
            
        except Exception as e:
            health['status'] = 'unhealthy'
            health['errors'].append(str(e))
            logger.error(f"Queue health check failed: {e}")
        
        return health


# Global queue manager instance
_queue_manager = None


def get_queue_manager() -> QueueManager:
    """Get the global queue manager instance."""
    global _queue_manager
    if _queue_manager is None:
        _queue_manager = QueueManager()
    return _queue_manager


def reset_queue_manager():
    """Reset the global queue manager instance."""
    global _queue_manager
    _queue_manager = None
