import os
import redis
from rq import Queue

# Connect to Redis
redis_client = redis.StrictRedis(host='localhost', port=6379, db=0)

# Initialize RQ queue
queue = Queue('email_paths', connection=redis_client)

# Directory containing email files
emails_directory = 'emails'

# Check if the directory exists
if os.path.exists(emails_directory):
    # Iterate through all files in the directory
    for email_file in os.listdir(emails_directory):
        email_path = os.path.join(emails_directory, email_file)
        # Ensure it's a file before adding to the queue
        if os.path.isfile(email_path):
            # Enqueue the file path as a task in the RQ queue
            queue.enqueue(email_path)
            print(f"Enqueued to queue: {email_path}")
else:
    print(f"Directory '{emails_directory}' does not exist.")