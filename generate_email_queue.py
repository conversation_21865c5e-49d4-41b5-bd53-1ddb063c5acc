import os
import redis
import json
import argparse
from config import get_config
from database import get_db_manager, initialize_database
from models import QueueItem

def main():
    """Generate email queue items for processing."""
    parser = argparse.ArgumentParser(description='Generate email queue for processing')
    parser.add_argument('--user-email', type=str, required=True,
                        help='Email address of the user')
    parser.add_argument('--emails-directory', type=str, default=None,
                        help='Directory containing email files (uses config if not provided)')
    parser.add_argument('--priority', type=int, default=0,
                        help='Priority for queue items (default: 0)')

    args = parser.parse_args()

    # Load configuration
    config = get_config()

    # Initialize database
    initialize_database()
    db = get_db_manager()

    # Get or create user
    user = db.get_or_create_user(args.user_email)
    print(f"Using user: {user.email} (ID: {user.id})")

    # Connect to Redis
    redis_client = redis.Redis(
        host=config.redis.host,
        port=config.redis.port,
        decode_responses=True
    )

    # Directory containing email files
    emails_directory = args.emails_directory or config.directories.email_folder

    # Check if the directory exists
    if not os.path.exists(emails_directory):
        print(f"Directory '{emails_directory}' does not exist.")
        return

    # Get list of email files
    email_files = [f for f in os.listdir(emails_directory) if f.endswith('.eml')]

    if not email_files:
        print(f"No .eml files found in '{emails_directory}'")
        return

    print(f"Found {len(email_files)} email files to queue")

    # Enqueue email files
    queued_count = 0
    for email_file in email_files:
        email_path = os.path.join(emails_directory, email_file)

        # Create queue item
        queue_item = QueueItem(
            user_id=user.id,
            email_path=email_path,
            priority=args.priority
        )

        # Serialize and enqueue
        queue_data = json.dumps(queue_item.dict())
        redis_client.rpush(config.redis.queue_name, queue_data)

        print(f"Queued: {email_file} for user {user.email}")
        queued_count += 1

    print(f"Successfully queued {queued_count} emails for processing")

    # Show queue status
    queue_length = redis_client.llen(config.redis.queue_name)
    print(f"Total items in queue: {queue_length}")

if __name__ == "__main__":
    main()