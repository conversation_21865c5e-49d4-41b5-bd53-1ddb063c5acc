import os
import redis
import multiprocessing
from processor import EmailProcessor  # Fix the import error by using absolute import

# Redis connection
redis_host = os.environ.get('REDIS_HOST', 'redis')
redis_port = int(os.environ.get('REDIS_PORT', 6379))
redis_client = redis.Redis(host=redis_host, port=redis_port)

def process_email_worker():
    """Worker function to process emails from the Redis queue"""
    processor = EmailProcessor()
    
    while True:
        # Pop email path from Redis queue
        email_path = redis_client.lpop('email_paths')
        if not email_path:
            # If queue is empty, wait and try again
            import time
            time.sleep(1)
            continue
            
        email_path = email_path.decode('utf-8')
        print(f"Processing email: {email_path}")
        
        try:
            processor.process(email_path)
        except Exception as e:
            print(f"Error processing email {email_path}: {e}")

if __name__ == "__main__":
    # Number of worker processes
    num_workers = int(os.environ.get('NUM_WORKERS', multiprocessing.cpu_count()))
    print(f"Starting {num_workers} worker processes")
    
    # Start worker processes
    processes = []
    for _ in range(num_workers):
        p = multiprocessing.Process(target=process_email_worker)
        p.start()
        processes.append(p)
    
    # Wait for all processes to complete
    for p in processes:
        p.join()