import redis
import multiprocessing
import json
import logging
from processor import EmailProcessor
from config import get_config
from models import QueueItem
from database import initialize_database

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_email_worker():
    """Worker function to process emails from the Redis queue"""
    # Load configuration
    config = get_config()

    # Initialize database
    initialize_database()

    # Redis connection
    redis_client = redis.Redis(
        host=config.redis.host,
        port=config.redis.port,
        decode_responses=True
    )

    logger.info(f"Worker started, connecting to Redis at {config.redis.host}:{config.redis.port}")

    while True:
        try:
            # Pop queue item from Redis queue
            queue_data = redis_client.lpop(config.redis.queue_name)
            if not queue_data:
                # If queue is empty, wait and try again
                import time
                time.sleep(1)
                continue

            # Parse queue item
            try:
                queue_item_data = json.loads(queue_data)
                queue_item = QueueItem(**queue_item_data)
            except (json.JSONDecodeError, TypeError) as e:
                logger.error(f"Failed to parse queue item: {queue_data}, error: {e}")
                continue

            logger.info(f"Processing email for user {queue_item.user_id}: {queue_item.email_path}")

            # Get user email from database to initialize processor
            from database import get_db_manager
            db = get_db_manager()

            # Get user by ID
            user_query = "SELECT * FROM users WHERE id = %s"
            user_data = db.execute_query(user_query, (queue_item.user_id,))

            if not user_data:
                logger.error(f"User not found: {queue_item.user_id}")
                continue

            user_email = user_data[0]['email']

            # Initialize processor with user context
            processor = EmailProcessor(
                user_email=user_email,
                email_folder=config.directories.email_folder,
                template_output_dir=config.directories.template_output_dir
            )

            # Process the email
            result = processor.process_email(queue_item.email_path)
            logger.info(f"Processed {queue_item.email_path} for user {user_email}: {result.status}")

        except Exception as e:
            logger.error(f"Error in worker process: {e}")
            import time
            time.sleep(5)  # Wait before retrying on error

if __name__ == "__main__":
    # Load configuration
    config = get_config()

    # Number of worker processes
    num_workers = config.processing.num_workers
    logger.info(f"Starting {num_workers} worker processes")

    try:
        # Start worker processes
        processes = []
        for i in range(num_workers):
            p = multiprocessing.Process(target=process_email_worker, name=f"EmailWorker-{i+1}")
            p.start()
            processes.append(p)
            logger.info(f"Started worker process {i+1}: PID {p.pid}")

        # Wait for all processes to complete
        for p in processes:
            p.join()

    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down workers...")
        for p in processes:
            p.terminate()
        for p in processes:
            p.join()
        logger.info("All workers shut down")