"""Main application entry point for email scanner workers."""

import sys
import signal
import logging
from typing import List
from rq import Worker, Connection
import redis

from .config.settings import get_config, set_environment
from .config.logging_config import setup_logging, get_logger
from .database.utils import ensure_database_ready
from .queue.manager import get_queue_manager
from .utils.exceptions import EmailScannerError, handle_exception

logger = get_logger('main')

class EmailScannerWorker:
    """Main worker class for processing email jobs."""
    
    def __init__(self, environment: str = None):
        """Initialize the email scanner worker."""
        # Set environment if provided
        if environment:
            set_environment(environment)
        
        # Load configuration
        self.config = get_config()
        
        # Setup logging
        setup_logging(
            log_level=self.config.log_level,
            log_file=self.config.log_file,
            environment=self.config.environment
        )
        
        logger.info(f"Initializing EmailScannerWorker for environment: {self.config.environment}")
        
        # Initialize components
        self.redis_conn = None
        self.workers = []
        self.shutdown_requested = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_requested = True
        self._shutdown_workers()
    
    def _initialize_redis(self):
        """Initialize Redis connection."""
        try:
            self.redis_conn = redis.from_url(self.config.redis.url)
            self.redis_conn.ping()
            logger.info(f"Connected to Redis at {self.config.redis.url}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise EmailScannerError(f"Redis connection failed: {e}")
    
    def _ensure_dependencies_ready(self):
        """Ensure all dependencies are ready before starting workers."""
        logger.info("Checking system dependencies...")
        
        try:
            # Ensure database is ready
            ensure_database_ready()
            logger.info("✓ Database is ready")
            
            # Initialize Redis connection
            self._initialize_redis()
            logger.info("✓ Redis is ready")
            
            # Test queue manager
            queue_manager = get_queue_manager()
            queue_health = queue_manager.health_check()
            if queue_health['status'] != 'healthy':
                raise EmailScannerError(f"Queue system unhealthy: {queue_health['errors']}")
            logger.info("✓ Queue system is ready")
            
        except Exception as e:
            logger.error(f"Dependency check failed: {e}")
            raise
    
    def start_workers(self, num_workers: int = None) -> List[Worker]:
        """Start RQ workers for email processing.
        
        Args:
            num_workers: Number of workers to start (uses config if not provided)
            
        Returns:
            List of started Worker instances
        """
        try:
            # Ensure dependencies are ready
            self._ensure_dependencies_ready()
            
            # Determine number of workers
            if num_workers is None:
                num_workers = self.config.processing.num_workers
            
            logger.info(f"Starting {num_workers} email processing workers...")
            
            # Create workers
            queue_manager = get_queue_manager()
            
            with Connection(self.redis_conn):
                for i in range(num_workers):
                    worker_name = f"email-worker-{i+1}"
                    worker = Worker(
                        [queue_manager.queue],
                        connection=self.redis_conn,
                        name=worker_name
                    )
                    self.workers.append(worker)
                    logger.info(f"Created worker: {worker_name}")
                
                # Start all workers
                logger.info("Starting workers...")
                for worker in self.workers:
                    try:
                        # Start worker in a separate process/thread
                        worker.work(burst=False, logging_level=self.config.log_level)
                    except Exception as e:
                        logger.error(f"Failed to start worker {worker.name}: {e}")
                        raise
            
            logger.info(f"Successfully started {len(self.workers)} workers")
            return self.workers
            
        except Exception as e:
            error = handle_exception(logger, e, "Starting workers")
            raise error
    
    def _shutdown_workers(self):
        """Gracefully shutdown all workers."""
        if not self.workers:
            return
        
        logger.info("Shutting down workers...")
        
        for worker in self.workers:
            try:
                logger.info(f"Stopping worker: {worker.name}")
                worker.request_stop()
            except Exception as e:
                logger.error(f"Error stopping worker {worker.name}: {e}")
        
        # Wait for workers to finish current jobs
        import time
        timeout = 30  # 30 seconds timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            active_workers = [w for w in self.workers if w.get_state() == 'busy']
            if not active_workers:
                break
            
            logger.info(f"Waiting for {len(active_workers)} workers to finish...")
            time.sleep(1)
        
        logger.info("All workers stopped")
    
    def run_single_worker(self, burst: bool = False):
        """Run a single worker (useful for development/testing).
        
        Args:
            burst: If True, worker will exit after processing all jobs
        """
        try:
            self._ensure_dependencies_ready()
            
            queue_manager = get_queue_manager()
            
            with Connection(self.redis_conn):
                worker = Worker([queue_manager.queue], connection=self.redis_conn)
                logger.info(f"Starting single worker (burst={burst})")
                worker.work(burst=burst, logging_level=self.config.log_level)
                
        except Exception as e:
            error = handle_exception(logger, e, "Running single worker")
            raise error
    
    def get_system_status(self) -> dict:
        """Get current system status."""
        try:
            from .database.utils import get_database_status
            
            status = {
                'environment': self.config.environment,
                'workers': len(self.workers),
                'shutdown_requested': self.shutdown_requested
            }
            
            # Add database status
            status['database'] = get_database_status()
            
            # Add queue status
            if self.redis_conn:
                try:
                    queue_manager = get_queue_manager()
                    status['queue'] = queue_manager.get_queue_info()
                except Exception as e:
                    status['queue'] = {'error': str(e)}
            else:
                status['queue'] = {'status': 'not_connected'}
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}


def main():
    """Main entry point for the application."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Email Scanner Worker')
    parser.add_argument(
        '--environment', 
        type=str, 
        choices=['local', 'development', 'production'],
        help='Environment to run in'
    )
    parser.add_argument(
        '--workers', 
        type=int, 
        help='Number of workers to start'
    )
    parser.add_argument(
        '--single', 
        action='store_true',
        help='Run single worker (for development)'
    )
    parser.add_argument(
        '--burst', 
        action='store_true',
        help='Run in burst mode (exit after processing all jobs)'
    )
    parser.add_argument(
        '--status', 
        action='store_true',
        help='Show system status and exit'
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize worker
        worker_app = EmailScannerWorker(environment=args.environment)
        
        if args.status:
            # Show status and exit
            status = worker_app.get_system_status()
            print("System Status:")
            for key, value in status.items():
                print(f"  {key}: {value}")
            return 0
        
        if args.single:
            # Run single worker
            logger.info("Running in single worker mode")
            worker_app.run_single_worker(burst=args.burst)
        else:
            # Run multiple workers
            workers = worker_app.start_workers(num_workers=args.workers)
            
            # Keep main thread alive
            try:
                while not worker_app.shutdown_requested:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt")
            finally:
                worker_app._shutdown_workers()
        
        logger.info("Email scanner worker shutdown complete")
        return 0
        
    except EmailScannerError as e:
        logger.error(f"Email scanner error: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
