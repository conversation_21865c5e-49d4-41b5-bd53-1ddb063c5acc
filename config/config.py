"""Configuration management for the email categorization system."""
import os
import logging
from typing import Optional, List
from dataclasses import dataclass
from dotenv import load_dotenv

from ..utils.exceptions import ConfigurationError

# Load environment variables from .env file if it exists
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    host: str
    port: int
    database: str
    user: str
    password: str
    min_connections: int = 1
    max_connections: int = 10

@dataclass
class RedisConfig:
    """Redis configuration settings."""
    host: str
    port: int
    url: str
    queue_name: str = "email_queue"

@dataclass
class ChromaDBConfig:
    """ChromaDB configuration settings."""
    host: str
    port: int

@dataclass
class OllamaConfig:
    """Ollama configuration settings."""
    base_url: str
    embedding_model: str = "nomic-embed-text"
    llm_model: str = "llama3"

@dataclass
class DirectoryConfig:
    """Directory configuration settings."""
    email_folder: str
    template_output_dir: str
    data_dir: str = "./data"

@dataclass
class ProcessingConfig:
    """Email processing configuration settings."""
    num_workers: int = 4
    batch_size: int = 10
    similarity_threshold: float = 0.8
    default_categories: List[str] = None

@dataclass
class AppConfig:
    """Main application configuration."""
    environment: str
    debug: bool
    log_level: str
    database: DatabaseConfig
    redis: RedisConfig
    chromadb: ChromaDBConfig
    ollama: OllamaConfig
    directories: DirectoryConfig
    processing: ProcessingConfig

class ConfigManager:
    """Manages application configuration for different environments."""
    
    def __init__(self, environment: str = None):
        """Initialize configuration manager."""
        self.environment = environment or os.getenv('ENVIRONMENT', 'local')
        self._config = None
    
    def get_config(self) -> AppConfig:
        """Get configuration for the current environment."""
        if self._config is None:
            self._config = self._load_config()
        return self._config
    
    def _load_config(self) -> AppConfig:
        """Load configuration based on environment."""
        if self.environment == 'production':
            return self._get_production_config()
        elif self.environment == 'development':
            return self._get_development_config()
        else:
            return self._get_local_config()
    
    def _get_local_config(self) -> AppConfig:
        """Get local development configuration."""
        return AppConfig(
            environment='local',
            debug=True,
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            database=DatabaseConfig(
                host=os.getenv('POSTGRES_HOST', 'localhost'),
                port=int(os.getenv('POSTGRES_PORT', '5432')),
                database=os.getenv('POSTGRES_DB', 'email_db'),
                user=os.getenv('POSTGRES_USER', 'admin'),
                password=os.getenv('POSTGRES_PASSWORD', 'admin123'),
                min_connections=1,
                max_connections=5
            ),
            redis=RedisConfig(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', '6379')),
                url=os.getenv('REDIS_URL', 'redis://localhost:6379'),
                queue_name=os.getenv('REDIS_QUEUE_NAME', 'email_queue')
            ),
            chromadb=ChromaDBConfig(
                host=os.getenv('CHROMADB_HOST', 'localhost'),
                port=int(os.getenv('CHROMADB_PORT', '8000'))
            ),
            ollama=OllamaConfig(
                base_url=os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434'),
                embedding_model=os.getenv('OLLAMA_EMBEDDING_MODEL', 'nomic-embed-text'),
                llm_model=os.getenv('OLLAMA_LLM_MODEL', 'llama3')
            ),
            directories=DirectoryConfig(
                email_folder=os.getenv('EMAIL_FOLDER', './emails'),
                template_output_dir=os.getenv('TEMPLATE_OUTPUT_DIR', './processed_email_templates'),
                data_dir=os.getenv('DATA_DIR', './data')
            ),
            processing=ProcessingConfig(
                num_workers=int(os.getenv('NUM_WORKERS', '2')),
                batch_size=int(os.getenv('BATCH_SIZE', '10')),
                similarity_threshold=float(os.getenv('SIMILARITY_THRESHOLD', '0.8')),
                default_categories=['business', 'social', 'marketing', 'personal', 'shopping', 'transaction']
            )
        )
    
    def _get_development_config(self) -> AppConfig:
        """Get development environment configuration."""
        return AppConfig(
            environment='development',
            debug=True,
            log_level=os.getenv('LOG_LEVEL', 'DEBUG'),
            database=DatabaseConfig(
                host=os.getenv('POSTGRES_HOST', 'postgres'),
                port=int(os.getenv('POSTGRES_PORT', '5432')),
                database=os.getenv('POSTGRES_DB', 'email_db'),
                user=os.getenv('POSTGRES_USER', 'admin'),
                password=os.getenv('POSTGRES_PASSWORD', 'admin123'),
                min_connections=2,
                max_connections=10
            ),
            redis=RedisConfig(
                host=os.getenv('REDIS_HOST', 'redis'),
                port=int(os.getenv('REDIS_PORT', '6379')),
                url=os.getenv('REDIS_URL', 'redis://redis:6379'),
                queue_name=os.getenv('REDIS_QUEUE_NAME', 'email_queue')
            ),
            chromadb=ChromaDBConfig(
                host=os.getenv('CHROMADB_HOST', 'chromadb'),
                port=int(os.getenv('CHROMADB_PORT', '8000'))
            ),
            ollama=OllamaConfig(
                base_url=os.getenv('OLLAMA_BASE_URL', 'http://ollama:11434'),
                embedding_model=os.getenv('OLLAMA_EMBEDDING_MODEL', 'nomic-embed-text'),
                llm_model=os.getenv('OLLAMA_LLM_MODEL', 'llama3')
            ),
            directories=DirectoryConfig(
                email_folder=os.getenv('EMAIL_FOLDER', '/app/emails'),
                template_output_dir=os.getenv('TEMPLATE_OUTPUT_DIR', '/app/processed_email_templates'),
                data_dir=os.getenv('DATA_DIR', '/app/data')
            ),
            processing=ProcessingConfig(
                num_workers=int(os.getenv('NUM_WORKERS', '4')),
                batch_size=int(os.getenv('BATCH_SIZE', '20')),
                similarity_threshold=float(os.getenv('SIMILARITY_THRESHOLD', '0.8')),
                default_categories=['business', 'social', 'marketing', 'personal', 'shopping', 'transaction']
            )
        )
    
    def _get_production_config(self) -> AppConfig:
        """Get production environment configuration."""
        return AppConfig(
            environment='production',
            debug=False,
            log_level=os.getenv('LOG_LEVEL', 'WARNING'),
            database=DatabaseConfig(
                host=os.getenv('POSTGRES_HOST', 'postgres'),
                port=int(os.getenv('POSTGRES_PORT', '5432')),
                database=os.getenv('POSTGRES_DB', 'email_db'),
                user=os.getenv('POSTGRES_USER', 'admin'),
                password=os.getenv('POSTGRES_PASSWORD', 'admin123'),
                min_connections=5,
                max_connections=20
            ),
            redis=RedisConfig(
                host=os.getenv('REDIS_HOST', 'redis'),
                port=int(os.getenv('REDIS_PORT', '6379')),
                url=os.getenv('REDIS_URL', 'redis://redis:6379'),
                queue_name=os.getenv('REDIS_QUEUE_NAME', 'email_queue')
            ),
            chromadb=ChromaDBConfig(
                host=os.getenv('CHROMADB_HOST', 'chromadb'),
                port=int(os.getenv('CHROMADB_PORT', '8000'))
            ),
            ollama=OllamaConfig(
                base_url=os.getenv('OLLAMA_BASE_URL', 'http://ollama:11434'),
                embedding_model=os.getenv('OLLAMA_EMBEDDING_MODEL', 'nomic-embed-text'),
                llm_model=os.getenv('OLLAMA_LLM_MODEL', 'llama3')
            ),
            directories=DirectoryConfig(
                email_folder=os.getenv('EMAIL_FOLDER', '/app/emails'),
                template_output_dir=os.getenv('TEMPLATE_OUTPUT_DIR', '/app/processed_email_templates'),
                data_dir=os.getenv('DATA_DIR', '/app/data')
            ),
            processing=ProcessingConfig(
                num_workers=int(os.getenv('NUM_WORKERS', '8')),
                batch_size=int(os.getenv('BATCH_SIZE', '50')),
                similarity_threshold=float(os.getenv('SIMILARITY_THRESHOLD', '0.8')),
                default_categories=['business', 'social', 'marketing', 'personal', 'shopping', 'transaction']
            )
        )

# Global configuration manager instance
_config_manager = None

def get_config() -> AppConfig:
    """Get the global configuration instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager.get_config()

def set_environment(environment: str):
    """Set the environment and reload configuration."""
    global _config_manager
    _config_manager = ConfigManager(environment)

def get_database_url() -> str:
    """Get database URL for SQLAlchemy or other ORMs."""
    config = get_config()
    db = config.database
    return f"postgresql://{db.user}:{db.password}@{db.host}:{db.port}/{db.database}"
