-- PostgreSQL Database Schema for Email Categorization System
-- This schema supports business profile management and user interaction tracking

-- Create database (run this separately if needed)
-- CREATE DATABASE email_db;

-- Users table to store user information
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Business entities table to store business profile information
CREATE TABLE IF NOT EXISTS business_entities (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
    domain VARCHAR(255),
    industry VARCHAR(100),
    description TEXT,
    website VARCHAR(255),
    contact_email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email categories table for standardized categorization
CREATE TABLE IF NOT EXISTS email_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default email categories
INSERT INTO email_categories (name, description) VALUES
    ('business', 'Business-related communications'),
    ('social', 'Social media notifications and updates'),
    ('marketing', 'Marketing emails and newsletters'),
    ('personal', 'Personal communications'),
    ('shopping', 'Shopping and e-commerce related emails'),
    ('transaction', 'Transaction confirmations and receipts'),
    ('support', 'Customer support communications'),
    ('notification', 'System notifications and alerts')
ON CONFLICT (name) DO NOTHING;

-- User-business interactions table to track communication history
CREATE TABLE IF NOT EXISTS user_business_interactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    business_entity_id INTEGER REFERENCES business_entities(id) ON DELETE CASCADE,
    email_category_id INTEGER REFERENCES email_categories(id),
    email_subject VARCHAR(500),
    email_from VARCHAR(255),
    email_date TIMESTAMP,
    interaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    email_file_path VARCHAR(500),
    template_id VARCHAR(100), -- Reference to ChromaDB document ID
    similarity_score FLOAT,
    processing_status VARCHAR(50) DEFAULT 'processed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email processing queue table to track processing status
CREATE TABLE IF NOT EXISTS email_processing_queue (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email_file_path VARCHAR(500) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
    priority INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- Business interaction summary table for quick analytics
CREATE TABLE IF NOT EXISTS business_interaction_summary (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    business_entity_id INTEGER REFERENCES business_entities(id) ON DELETE CASCADE,
    total_interactions INTEGER DEFAULT 0,
    last_interaction_date TIMESTAMP,
    most_common_category_id INTEGER REFERENCES email_categories(id),
    first_interaction_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, business_entity_id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_business_entities_name ON business_entities(name);
CREATE INDEX IF NOT EXISTS idx_business_entities_domain ON business_entities(domain);
CREATE INDEX IF NOT EXISTS idx_user_business_interactions_user_id ON user_business_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_business_interactions_business_id ON user_business_interactions(business_entity_id);
CREATE INDEX IF NOT EXISTS idx_user_business_interactions_date ON user_business_interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_email_processing_queue_status ON email_processing_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_processing_queue_user_id ON email_processing_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_business_summary_user_business ON business_interaction_summary(user_id, business_entity_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_entities_updated_at BEFORE UPDATE ON business_entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_processing_queue_updated_at BEFORE UPDATE ON email_processing_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_interaction_summary_updated_at BEFORE UPDATE ON business_interaction_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update business interaction summary
CREATE OR REPLACE FUNCTION update_business_interaction_summary()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert or update the summary record
    INSERT INTO business_interaction_summary (
        user_id, 
        business_entity_id, 
        total_interactions, 
        last_interaction_date,
        first_interaction_date
    )
    VALUES (
        NEW.user_id, 
        NEW.business_entity_id, 
        1, 
        NEW.interaction_date,
        NEW.interaction_date
    )
    ON CONFLICT (user_id, business_entity_id) 
    DO UPDATE SET
        total_interactions = business_interaction_summary.total_interactions + 1,
        last_interaction_date = GREATEST(business_interaction_summary.last_interaction_date, NEW.interaction_date),
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update business interaction summary
CREATE TRIGGER update_business_summary_trigger 
    AFTER INSERT ON user_business_interactions
    FOR EACH ROW EXECUTE FUNCTION update_business_interaction_summary();
