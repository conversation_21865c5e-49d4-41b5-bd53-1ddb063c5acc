"""Command-line interface for email categorization system."""
import os
import argparse
import sys

from .processor import EmailProcessor

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='Email Categorization and Template Extraction')

    # Required arguments
    parser.add_argument('--email-folder', type=str, default='emails',
                        help='Folder containing email files (.eml)')
    parser.add_argument('--template-output-dir', type=str, default='processed_email_templates',
                        help='Directory to save processed templates')

    # ChromaDB connection arguments
    parser.add_argument('--chromadb-host', type=str, default=None,
                        help='ChromaDB server host (uses CHROMADB_HOST env var if not provided)')
    parser.add_argument('--chromadb-port', type=int, default=None,
                        help='ChromaDB server port (uses CHROMADB_PORT env var if not provided)')

    # Optional arguments
    parser.add_argument('--single-file', type=str,
                        help='Process a single email file instead of the entire folder')
    parser.add_argument('--list-categories', action='store_true',
                        help='List all categories in the database')
    parser.add_argument('--list-entities', action='store_true',
                        help='List all business entities in the database')
    parser.add_argument('--export-templates', type=str,
                        help='Export all templates to the specified folder')

    args = parser.parse_args()

    # Create processor
    processor = EmailProcessor(
        email_folder=args.email_folder,
        template_output_dir=args.template_output_dir,
        chromadb_host=args.chromadb_host,
        chromadb_port=args.chromadb_port
    )
    
    # Handle different commands
    if args.single_file:
        if not os.path.exists(args.single_file):
            print(f"Error: File '{args.single_file}' does not exist.")
            sys.exit(1)
        
        result = processor.process_email(args.single_file)
        
        print(f"File: {os.path.basename(args.single_file)}")
        print(f"Category: {result.category}")
        print(f"Business Entity: {result.business_entity}")
        print(f"Status: {result.status}")
        
        if result.matched_with:
            print(f"Matched with: {result.matched_with}")
            print(f"Similarity score: {result.similarity_score:.6f}")
        
        print("\nTemplate:")
        print("-" * 50)
        print(result.template)
        
    elif args.list_categories:
        # This would require implementing a method to list categories from the vector store
        print("List categories functionality not yet implemented")

    elif args.list_entities:
        # This would require implementing a method to list entities from the vector store
        print("List entities functionality not yet implemented")

    elif args.export_templates:
        # This would require implementing a method to export templates from the vector store
        print("Export templates functionality not yet implemented")

    else:
        # Default behavior: process all emails
        processor.process_emails()

if __name__ == "__main__":
    main()