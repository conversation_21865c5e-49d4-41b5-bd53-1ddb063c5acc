"""Command-line interface for email categorization system."""
import os
import argparse
import sys
from pathlib import Path

from .processor import EmailProcessor

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='Email Categorization and Template Extraction')
    
    # Required arguments
    parser.add_argument('--email-folder', type=str, default='emails',
                        help='Folder containing email files (.eml)')
    parser.add_argument('--db-path', type=str, default='vectordb',
                        help='Path to store the vector database')
    
    # Optional arguments
    parser.add_argument('--single-file', type=str, 
                        help='Process a single email file instead of the entire folder')
    parser.add_argument('--list-categories', action='store_true',
                        help='List all categories in the database')
    parser.add_argument('--list-entities', action='store_true',
                        help='List all business entities in the database')
    parser.add_argument('--export-templates', type=str,
                        help='Export all templates to the specified folder')
    
    args = parser.parse_args()
    
    # Create processor
    processor = EmailProcessor(
        email_folder=args.email_folder,
        db_path=args.db_path
    )
    
    # Handle different commands
    if args.single_file:
        if not os.path.exists(args.single_file):
            print(f"Error: File '{args.single_file}' does not exist.")
            sys.exit(1)
        
        result = processor.process_email(args.single_file)
        
        print(f"File: {os.path.basename(args.single_file)}")
        print(f"Category: {result.category}")
        print(f"Business Entity: {result.business_entity}")
        print(f"Status: {result.status}")
        
        if result.matched_with:
            print(f"Matched with: {result.matched_with}")
            print(f"Similarity score: {result.similarity_score:.6f}")
        
        print("\nTemplate:")
        print("-" * 50)
        print(result.template)
        
    elif args.list_categories:
        # This would