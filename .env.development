# Development environment configuration (Docker)
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Database configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=email_db
POSTGRES_USER=admin
POSTGRES_PASSWORD=admin123

# Redis configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_URL=redis://redis:6379
REDIS_QUEUE_NAME=email_queue

# ChromaDB configuration
CHROMADB_HOST=chromadb
CHROMADB_PORT=8000

# Ollama configuration
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
OLLAMA_LLM_MODEL=llama3

# Directory configuration
EMAIL_FOLDER=/app/emails
TEMPLATE_OUTPUT_DIR=/app/processed_email_templates
DATA_DIR=/app/data

# Processing configuration
NUM_WORKERS=4
BATCH_SIZE=20
SIMILARITY_THRESHOLD=0.8
