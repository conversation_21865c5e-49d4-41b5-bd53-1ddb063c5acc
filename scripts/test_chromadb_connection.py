#!/usr/bin/env python3
"""Test script to verify ChromaDB connection and VectorStore functionality."""

import os
import sys
from storage import VectorStore
from llm_chains import LLMChainFactory

def test_chromadb_connection():
    """Test ChromaDB connection and basic VectorStore operations."""
    
    # Get ChromaDB connection parameters from environment
    chromadb_host = os.getenv('CHROMADB_HOST', 'localhost')
    chromadb_port = int(os.getenv('CHROMADB_PORT', '8000'))
    
    print(f"Testing ChromaDB connection to {chromadb_host}:{chromadb_port}")
    
    try:
        # Initialize embeddings
        print("Initializing embeddings...")
        embeddings = LLMChainFactory.create_embeddings()
        print("✓ Embeddings initialized successfully")
        
        # Initialize VectorStore
        print("Initializing VectorStore...")
        vector_store = VectorStore(
            embeddings=embeddings,
            chromadb_host=chromadb_host,
            chromadb_port=chromadb_port
        )
        print("✓ VectorStore initialized successfully")
        
        # Test adding a document
        print("Testing document addition...")
        test_doc_id = "test_doc_123"
        test_text = "This is a test email template for testing purposes."
        test_metadata = {
            "category": "test",
            "business_entity": "test_company",
            "filename": "test.eml"
        }
        
        vector_store.add_document(
            text=test_text,
            metadata=test_metadata,
            doc_id=test_doc_id
        )
        print("✓ Document added successfully")
        
        # Test duplicate check
        print("Testing duplicate check...")
        is_duplicate = vector_store.check_duplicate(test_doc_id)
        if is_duplicate:
            print("✓ Duplicate check working correctly")
        else:
            print("✗ Duplicate check failed")
            
        # Test similarity search
        print("Testing similarity search...")
        is_similar, metadata, score = vector_store.find_similar(test_text)
        if is_similar:
            print(f"✓ Similarity search working correctly (score: {score})")
        else:
            print("✓ Similarity search working (no similar documents found)")
            
        print("\n🎉 All tests passed! ChromaDB connection is working correctly.")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_chromadb_connection()
    sys.exit(0 if success else 1)
