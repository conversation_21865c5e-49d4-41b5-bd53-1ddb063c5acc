#!/usr/bin/env python3
"""Queue management script for email processing."""

import sys
import os
import argparse
import json
from typing import List

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from email_scanner.config.settings import set_environment, get_config
from email_scanner.config.logging_config import setup_logging, get_logger
from email_scanner.database.utils import ensure_database_ready
from email_scanner.database.manager import get_db_manager
from email_scanner.queue.manager import get_queue_manager
from email_scanner.utils.exceptions import EmailScannerError

logger = get_logger('queue_manager')

def enqueue_emails(user_email: str, 
                  emails_directory: str = None, 
                  email_files: List[str] = None,
                  priority: int = 0) -> dict:
    """Enqueue emails for processing."""
    try:
        # Ensure system is ready
        ensure_database_ready()
        
        # Get or create user
        db = get_db_manager()
        user = db.get_or_create_user(user_email)
        logger.info(f"Using user: {user.email} (ID: {user.id})")
        
        # Get queue manager
        queue_manager = get_queue_manager()
        
        # Determine email files to enqueue
        if email_files:
            # Use provided email files
            email_paths = email_files
        else:
            # Scan directory for email files
            config = get_config()
            emails_directory = emails_directory or config.directories.email_folder
            
            if not os.path.exists(emails_directory):
                raise EmailScannerError(f"Email directory does not exist: {emails_directory}")
            
            email_paths = [
                os.path.join(emails_directory, f)
                for f in os.listdir(emails_directory)
                if f.endswith('.eml')
            ]
        
        if not email_paths:
            logger.warning("No email files found to enqueue")
            return {
                'status': 'success',
                'user_id': user.id,
                'user_email': user.email,
                'total_emails': 0,
                'enqueued': 0,
                'job_ids': []
            }
        
        logger.info(f"Found {len(email_paths)} email files to enqueue")
        
        # Enqueue emails
        job_ids = []
        failed_count = 0
        
        for email_path in email_paths:
            try:
                job = queue_manager.enqueue_email_processing(
                    user_id=user.id,
                    email_path=email_path,
                    priority=priority
                )
                job_ids.append(job.id)
                logger.info(f"Enqueued: {os.path.basename(email_path)} -> Job {job.id}")
                
            except Exception as e:
                failed_count += 1
                logger.error(f"Failed to enqueue {email_path}: {e}")
        
        result = {
            'status': 'success',
            'user_id': user.id,
            'user_email': user.email,
            'total_emails': len(email_paths),
            'enqueued': len(job_ids),
            'failed': failed_count,
            'job_ids': job_ids
        }
        
        logger.info(f"Enqueue complete: {len(job_ids)} enqueued, {failed_count} failed")
        return result
        
    except Exception as e:
        logger.error(f"Error enqueuing emails: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }

def get_queue_status() -> dict:
    """Get queue status information."""
    try:
        queue_manager = get_queue_manager()
        return queue_manager.get_queue_info()
    except Exception as e:
        logger.error(f"Error getting queue status: {e}")
        return {'error': str(e)}

def get_job_status(job_id: str) -> dict:
    """Get status of a specific job."""
    try:
        queue_manager = get_queue_manager()
        return queue_manager.get_job_status(job_id)
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        return {'error': str(e)}

def clear_failed_jobs() -> dict:
    """Clear all failed jobs from the queue."""
    try:
        queue_manager = get_queue_manager()
        count = queue_manager.clear_failed_jobs()
        return {
            'status': 'success',
            'cleared_jobs': count
        }
    except Exception as e:
        logger.error(f"Error clearing failed jobs: {e}")
        return {'error': str(e)}

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Email Queue Management')
    
    # Global arguments
    parser.add_argument(
        '--environment',
        type=str,
        choices=['local', 'development', 'production'],
        help='Environment to run in'
    )
    
    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Enqueue command
    enqueue_parser = subparsers.add_parser('enqueue', help='Enqueue emails for processing')
    enqueue_parser.add_argument('--user-email', type=str, required=True, help='User email address')
    enqueue_parser.add_argument('--emails-directory', type=str, help='Directory containing email files')
    enqueue_parser.add_argument('--priority', type=int, default=0, help='Job priority')
    enqueue_parser.add_argument('email_files', nargs='*', help='Specific email files to enqueue')
    
    # Status command
    subparsers.add_parser('status', help='Show queue status')
    
    # Job status command
    job_parser = subparsers.add_parser('job-status', help='Show status of a specific job')
    job_parser.add_argument('job_id', help='Job ID to check')
    
    # Clear failed jobs command
    subparsers.add_parser('clear-failed', help='Clear all failed jobs')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        # Set environment
        if args.environment:
            set_environment(args.environment)
        
        # Setup logging
        config = get_config()
        setup_logging(config.log_level, None, config.environment)
        
        # Execute command
        if args.command == 'enqueue':
            result = enqueue_emails(
                user_email=args.user_email,
                emails_directory=args.emails_directory,
                email_files=args.email_files if args.email_files else None,
                priority=args.priority
            )
            print(json.dumps(result, indent=2))
            
        elif args.command == 'status':
            status = get_queue_status()
            print(json.dumps(status, indent=2))
            
        elif args.command == 'job-status':
            status = get_job_status(args.job_id)
            print(json.dumps(status, indent=2, default=str))
            
        elif args.command == 'clear-failed':
            result = clear_failed_jobs()
            print(json.dumps(result, indent=2))
        
        return 0
        
    except EmailScannerError as e:
        logger.error(f"Email scanner error: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
